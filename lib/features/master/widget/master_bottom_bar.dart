import 'package:albalad_parking_app/features/all_services/providers/all_services_provider.dart';
import 'package:albalad_parking_app/features/favourite/provider/favourite_list_provider.dart';
import 'package:albalad_parking_app/features/home/<USER>/location_provider.dart';
import 'package:albalad_parking_app/features/master/widget/fade_indexstack.dart';
import 'package:albalad_parking_app/features/my_parking/providers/active_parking_provider.dart';
import 'package:albalad_parking_app/features/my_parking/providers/my_parking_provider.dart';
import 'package:albalad_parking_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_parking_app/features/valet/valet_listing/provider/valet_active_listing_provider.dart';
import 'package:albalad_parking_app/features/wallet/providers/wallet_balance_provider.dart';
import 'package:albalad_parking_app/providers/global_providers.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MasterBottombar extends ConsumerStatefulWidget {
  final bool isForBottomSheet;
  const MasterBottombar({super.key, this.isForBottomSheet = false});

  @override
  ConsumerState<MasterBottombar> createState() => _MasterBottombarState();
}

class _MasterBottombarState extends ConsumerState<MasterBottombar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: !widget.isForBottomSheet
          ? EdgeInsets.only(bottom: 25.h, left: 15.w, right: 15.w)
          : null,
      height: 70.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.16),
            offset: Offset(2, 2),
            blurRadius: 15,
          ),
        ],
        borderRadius: BorderRadius.circular(35),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(35),
          color: ThemeColors.primaryColor,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: 66.h,
        child: Row(
          spacing: 48.h,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: bottomBarItem(),
        ),
      ),
    );
  }

  onChanged(BuildContext context, {required int index, mounted = true}) async {
    if (index < ref.watch(masterIndexProvider)) {
      biginOffset = const Offset(-1, 0);
    } else {
      biginOffset = const Offset(1, 0);
    }

    ref.read(masterIndexProvider.notifier).state = index;

    if (widget.isForBottomSheet) {
      Navigator.pop(context);
    }

    switch (index) {
      case 0:
        await _onIndexZeroTapped(context: context);
        break;
      case 1:
        await _onIndexOneTapped(context: context);
        break;
      case 2:
        await _onIndexTwoTapped(context: context);
        break;
      case 3:
        _onIndexThreeTapped(context: context);
        break;
      case 4:
        _onIndexFourTapped(context: context);
        break;
    }
  }

  _onIndexZeroTapped({required BuildContext context}) {
    ref.invalidate(activeParkingProvider);
    ref.invalidate(myParkingProvider);
    ref.read(valetActiveListingNotifierProvider.notifier).getValetActiveList();
    ref
        .read(locationFetchNotifierProvider.notifier)
        .getLocationFetchAll(context: context);
  }

  _onIndexOneTapped({required BuildContext context}) {
    ref.invalidate(activeParkingProvider);
    ref.invalidate(myParkingProvider);
    ref.read(valetActiveListingNotifierProvider.notifier).getValetActiveList();
  }

  _onIndexTwoTapped({required BuildContext context}) {
    ref.invalidate(allServicesProvider);
  }

  _onIndexThreeTapped({required BuildContext context}) {
    ref.watch(favouriteListNotifierProvider.notifier).reset();
  }

  _onIndexFourTapped({required BuildContext context}) {
    ref.invalidate(walletBalanceProvider);
    ref.read(profileNotifierProvider.notifier).getProfileData();
  }

  bottomBarItem() {
    return bottomBarItems.map((e) {
      int index = bottomBarItems.indexOf(e);
      String icon = e.iconUnselected;
      if (index == ref.watch(masterIndexProvider)) {
        icon = e.icon;
      }
      return Expanded(
        child: InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () => onChanged(context, index: index),
          child: Center(
            child: Image.asset(
              icon,
              height: 24.h,
              width: 24.w,
              // color: color,
            ),
          ),
        ),
      );
    }).toList();
  }
}

List<BottomBarItem> bottomBarItems = [
  BottomBarItem(
    icon: 'home_selected'.asIconPng(),
    iconUnselected: 'home'.asIconPng(),
  ),
  BottomBarItem(
    icon: 'parking_selected'.asIconPng(),
    iconUnselected: 'parking'.asIconPng(),
  ),
  BottomBarItem(
    icon: 'services_selected'.asIconPng(),
    iconUnselected: 'services'.asIconPng(),
  ),
  BottomBarItem(
    icon: 'star_selected'.asIconPng(),
    iconUnselected: 'heart'.asIconPng(),
  ),
  BottomBarItem(
    icon: 'user_selected'.asIconPng(),
    iconUnselected: 'user'.asIconPng(),
  ),
];

class BottomBarItem {
  String icon;
  String iconUnselected;
  BottomBarItem({required this.icon, required this.iconUnselected});
}
