import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/features/vehilces/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/utils/text_style.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ScanPlate extends StatelessWidget {
  final Function(String?) onScan;
  const ScanPlate({required this.onScan, super.key});

  String _getLabel({required String key}) =>
      translateLabelFunction(key: key, screen: 'booking-vehicle-screen');

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        var string =
            await Navigator.pushNamed(context, NumberPlateScanner.route);
        if (string != null) {
          onScan(string.toString());
        }
      },
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(6),
        dashPattern: const [10, 4],
        color: ThemeColors.colorE1DDD2,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: ThemeColors.colorE1DDD2,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            children: [
              Image.asset(
                'assets/images/scan-plate.png',
                width: 120.89.w,
                height: 69.h,
              ),
              Gap(10.26.h),
              Text(
                _getLabel(key: 'scan_label'),
                // 'Scan your license plate to auto-fill the fields',
                style: ts14w600c44322D,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
