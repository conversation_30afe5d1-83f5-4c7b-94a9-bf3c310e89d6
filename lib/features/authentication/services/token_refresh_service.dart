import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:albalad_parking_app/helper/secure_storage_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Service responsible for handling token refresh operations
/// This service manages the refresh token flow and updates stored tokens
class TokenRefreshService {
  static final TokenRefreshService _instance = TokenRefreshService._internal();
  factory TokenRefreshService() => _instance;
  TokenRefreshService._internal();

  // Create a separate Dio instance for token refresh to avoid circular dependency
  late final Dio _dio;
  bool _isRefreshing = false;
  bool _isInitialized = false;
  final List<Completer<String?>> _refreshCompleters = [];

  /// Initialize the service with a clean Dio instance
  void initialize() {
    if (_isInitialized) return; // Prevent multiple initialization

    _dio = Dio();
    _dio.options.baseUrl = ApiConstants.baseURL;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _isInitialized = true;
  }

  /// Refresh the access token using the stored refresh token
  /// Returns the new access token if successful, null if failed
  Future<String?> refreshToken() async {
    // Ensure the service is initialized
    if (!_isInitialized) {
      initialize();
    }

    // Prevent multiple simultaneous refresh attempts
    if (_isRefreshing) {
      final completer = Completer<String?>();
      _refreshCompleters.add(completer);
      return completer.future;
    }

    _isRefreshing = true;

    try {
      final refreshToken = Token.refreshToken;

      if (refreshToken == null || refreshToken.isEmpty) {
        if (kDebugMode) {
          log('No refresh token available', name: 'TokenRefreshService');
        }
        return null;
      }

      if (kDebugMode) {
        log('Attempting to refresh token', name: 'TokenRefreshService');
      }

      // Prepare form data
      final formData = FormData.fromMap({
        'refresh_token': refreshToken,
      });

      final response = await _dio.post(
        ApiConstants.refreshTokenURL,
        data: formData,
        options: Options(
          headers: await Api.headers(),
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      if (kDebugMode) {
        log('Refresh token response: ${response.statusCode}',
            name: 'TokenRefreshService');
        log('Response data: ${response.data}', name: 'TokenRefreshService');
      }

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Check if the response indicates success
          if (responseData['result'] == 'success' &&
              responseData['token'] != null) {
            final tokenData = responseData['token'];

            // Update the static token values
            Token.accessToken = tokenData['access_token'];
            Token.refreshToken = tokenData['refresh_token'];
            Token.expiresIn = tokenData['expires_in'];

            // Update stored user data with new tokens
            await _updateStoredTokens(tokenData);

            final newAccessToken = Token.accessToken;

            // Notify all waiting requests
            for (final completer in _refreshCompleters) {
              completer.complete(newAccessToken);
            }
            _refreshCompleters.clear();

            if (kDebugMode) {
              log('Token refreshed successfully', name: 'TokenRefreshService');
            }

            return newAccessToken;
          }
        }
      }

      // If we reach here, the refresh failed
      if (kDebugMode) {
        log('Token refresh failed with status: ${response.statusCode}',
            name: 'TokenRefreshService');
      }

      // Notify waiting requests of failure
      for (final completer in _refreshCompleters) {
        completer.complete(null);
      }
      _refreshCompleters.clear();

      return null;
    } catch (e) {
      if (kDebugMode) {
        log('Token refresh error: $e', name: 'TokenRefreshService');
      }

      // Notify waiting requests of failure
      for (final completer in _refreshCompleters) {
        completer.complete(null);
      }
      _refreshCompleters.clear();

      return null;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Update the stored user data with new token information
  Future<void> _updateStoredTokens(Map<String, dynamic> tokenData) async {
    try {
      // Get current user data from secure storage
      final userJson = await SecureStorageHelper.instance.getData('user');

      if (userJson != null) {
        final userData = jsonDecode(userJson);

        // Update token data
        userData['token'] = tokenData;

        // Save updated user data back to secure storage
        await SecureStorageHelper.instance
            .saveData('user', jsonEncode(userData));

        if (kDebugMode) {
          log('Stored tokens updated successfully',
              name: 'TokenRefreshService');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('Failed to update stored tokens: $e', name: 'TokenRefreshService');
      }
    }
  }

  /// Check if a refresh token is available
  bool hasRefreshToken() {
    return Token.refreshToken != null && Token.refreshToken!.isNotEmpty;
  }

  /// Clear all token refresh state (useful for logout)
  void clearRefreshState() {
    _isRefreshing = false;
    for (final completer in _refreshCompleters) {
      completer.complete(null);
    }
    _refreshCompleters.clear();
  }
}
