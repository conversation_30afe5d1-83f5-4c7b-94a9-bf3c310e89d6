/// Example service demonstrating proper token refresh integration
/// This file shows how to create services that work seamlessly with the token refresh system

import 'dart:developer';
import 'package:albalad_parking_app/app/dio/dio_client.dart';
import 'package:albalad_parking_app/core/error/error_handler.dart';
import 'package:albalad_parking_app/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Example service showing best practices for token refresh integration
class TokenRefreshExampleService {
  final dio = DioClient().dio; // ✅ Use DioClient for automatic token refresh
  CancelToken? _cancelToken;

  /// Example of a protected API call that will automatically handle token refresh
  Future<Map<String, dynamic>?> getProtectedData() async {
    try {
      // Cancel any previous request
      if (_cancelToken != null && !_cancelToken!.isCancelled) {
        _cancelToken!.cancel("Cancelled previous request");
      }
      _cancelToken = CancelToken();

      if (kDebugMode) {
        log('Making protected API call', name: 'TokenRefreshExample');
      }

      final response = await dio.get(
        '${ApiConstants.baseURL}protected-endpoint/',
        options: Options(
          headers: await Api.getAuthorizationHeader(),
          // ✅ Don't use validateStatus: (status) => true
          // This allows the AuthInterceptor to handle 401 responses
        ),
        cancelToken: _cancelToken,
      );

      if (response.statusCode == 200) {
        if (kDebugMode) {
          log('Protected data retrieved successfully', name: 'TokenRefreshExample');
        }
        return response.data as Map<String, dynamic>?;
      }

      return null;
    } on DioException catch (e) {
      if (kDebugMode) {
        log('API call failed: ${e.message}', name: 'TokenRefreshExample');
      }

      // Handle the error using the centralized error handler
      final appError = ErrorHandler.handleDioException(e, context: 'getProtectedData');
      
      // The AuthInterceptor will have already handled 401 errors by:
      // 1. Attempting token refresh
      // 2. Retrying the request with new token
      // 3. Or redirecting to login if refresh failed
      
      if (appError.isAuthError) {
        if (kDebugMode) {
          log('Authentication error occurred - user should be redirected to login', 
              name: 'TokenRefreshExample');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        log('Unexpected error: $e', name: 'TokenRefreshExample');
      }
      return null;
    }
  }

  /// Example of a POST request with form data
  Future<bool> submitProtectedData(Map<String, dynamic> data) async {
    try {
      if (_cancelToken != null && !_cancelToken!.isCancelled) {
        _cancelToken!.cancel("Cancelled previous request");
      }
      _cancelToken = CancelToken();

      final formData = FormData.fromMap(data);

      final response = await dio.post(
        '${ApiConstants.baseURL}submit-data/',
        data: formData,
        options: Options(
          headers: await Api.getAuthorizationHeader(),
          // ✅ Let the interceptor handle 401 responses
        ),
        cancelToken: _cancelToken,
      );

      return response.statusCode == 200;
    } on DioException catch (e) {
      final appError = ErrorHandler.handleDioException(e, context: 'submitProtectedData');
      
      if (appError.isAuthError) {
        // Auth error was handled by interceptor
        if (kDebugMode) {
          log('Auth error in submit - handled by interceptor', name: 'TokenRefreshExample');
        }
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        log('Submit error: $e', name: 'TokenRefreshExample');
      }
      return false;
    }
  }

  /// Example showing how to handle specific error cases
  Future<String?> getDataWithSpecificErrorHandling() async {
    try {
      final response = await dio.get(
        '${ApiConstants.baseURL}specific-endpoint/',
        options: Options(
          headers: await Api.getAuthorizationHeader(),
        ),
      );

      if (response.statusCode == 200) {
        return response.data['message'] as String?;
      }

      return null;
    } on DioException catch (e) {
      // Handle specific error cases
      switch (e.response?.statusCode) {
        case 401:
          // This case should rarely be reached because the AuthInterceptor
          // handles 401 responses automatically. If we reach here, it means
          // the token refresh failed and the user was redirected to login.
          if (kDebugMode) {
            log('401 error - token refresh must have failed', name: 'TokenRefreshExample');
          }
          return 'Authentication failed';
          
        case 403:
          if (kDebugMode) {
            log('403 Forbidden - user lacks permission', name: 'TokenRefreshExample');
          }
          return 'Access denied';
          
        case 404:
          if (kDebugMode) {
            log('404 Not Found', name: 'TokenRefreshExample');
          }
          return 'Resource not found';
          
        case 500:
          if (kDebugMode) {
            log('500 Server Error', name: 'TokenRefreshExample');
          }
          return 'Server error';
          
        default:
          final appError = ErrorHandler.handleDioException(e, context: 'getDataWithSpecificErrorHandling');
          return appError.message;
      }
    } catch (e) {
      if (kDebugMode) {
        log('Unexpected error: $e', name: 'TokenRefreshExample');
      }
      return 'An unexpected error occurred';
    }
  }

  /// Clean up resources
  void dispose() {
    _cancelToken?.cancel("Service disposed");
  }
}

/// ❌ WRONG EXAMPLE - Don't do this
class BadExampleService {
  final dio = Dio(); // ❌ Wrong - separate Dio instance without AuthInterceptor

  Future<Response> badApiCall() async {
    return await dio.get(
      '${ApiConstants.baseURL}data/',
      options: Options(
        headers: await Api.getAuthorizationHeader(),
        validateStatus: (status) => true, // ❌ Wrong - bypasses error handling
      ),
    );
  }
}

/// ✅ GOOD EXAMPLE - Correct pattern
class GoodExampleService {
  final dio = DioClient().dio; // ✅ Correct - uses DioClient with AuthInterceptor

  Future<Response> goodApiCall() async {
    return await dio.get(
      '${ApiConstants.baseURL}data/',
      options: Options(
        headers: await Api.getAuthorizationHeader(),
        // ✅ Correct - no validateStatus, allows proper error handling
      ),
    );
  }
}
