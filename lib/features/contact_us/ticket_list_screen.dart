import 'package:albalad_parking_app/features/contact_us/providers/ticket_list_provider.dart';
import 'package:albalad_parking_app/features/contact_us/seek_help_screen.dart';
import 'package:albalad_parking_app/features/contact_us/widget/ticket_card.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/custom_appbar.dart';
import 'package:albalad_parking_app/widgets/custom_gradient_spinner.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TicketListScreen extends ConsumerStatefulWidget {
  static const route = '/ticket_list_screen';
  const TicketListScreen({super.key});

  @override
  ConsumerState<TicketListScreen> createState() => _TicketListScreenState();
}

class _TicketListScreenState extends ConsumerState<TicketListScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch tickets when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ticketListProvider.notifier).fetchTickets();
    });
  }

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');

    final ticketListState = ref.watch(ticketListProvider);

    return Scaffold(
      appBar: albaladAppBar(
        context: context,
        title: getLabel(key: 'support'),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, SeekHelpScreen.route);
        },
        icon: const Icon(Icons.help_outline),
        label: Text(getLabel(key: 'seek_help')),
        backgroundColor: const Color(0xFF44322D),
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(ticketListProvider.notifier).refreshTickets();
        },
        child: ticketListState.when(
          loading: () => const Center(
            child: CustomGradientSpinner(),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64.r,
                    color: Colors.red,
                  ),
                  H(16),
                  Text(
                    getLabel(key: 'something_went_wrong'),
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  H(8),
                  Text(
                    error.toString(),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  H(24),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(ticketListProvider.notifier).fetchTickets();
                    },
                    child: Text(getLabel(key: 'retry')),
                  ),
                ],
              ),
            ),
          ),
          data: (ticketListResponse) {
            if (ticketListResponse == null) {
              return const Center(
                child: CustomGradientSpinner(),
              );
            }

            final tickets = ticketListResponse.tickets;

            if (tickets.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.support_agent_outlined,
                        size: 64.r,
                        color: Colors.grey[400],
                      ),
                      H(16),
                      Text(
                        getLabel(key: 'no_support_tickets_found'),
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      H(8),
                      Text(
                        getLabel(key: 'no_support_tickets_found_sub'),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      H(24),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, SeekHelpScreen.route);
                        },
                        icon: const Icon(Icons.help_outline),
                        label: Text(getLabel(key: 'seek_help')),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF44322D),
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: 24.w,
                            vertical: 12.h,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: tickets.length,
              itemBuilder: (context, index) {
                final ticket = tickets[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: TicketCard(ticket: ticket),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
