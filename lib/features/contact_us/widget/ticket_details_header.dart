import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/utils/text_style.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TicketDetailsHeader extends StatelessWidget {
  final TicketDetails ticket;

  const TicketDetailsHeader({
    super.key,
    required this.ticket,
  });

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');
    return Container(
      width: double.infinity,
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ThemeColors.colorEAEAEA,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with ticket number and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${getLabel(key: 'ticket')} ${ticket.ticketNumber}',
                style: ts18w6c181818,
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 6.h,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(ticket.status),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  ticket.status,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: _getStatusTextColor(ticket.status),
                  ),
                ),
              ),
            ],
          ),
          H(16),

          // Subject
          Text(
            ticket.subject,
            style: ts16w5c181818,
          ),
          H(12),

          // Description
          Html(
            data: ticket.description,
            style: {
              "body": Style(
                fontSize: FontSize(14.sp),
                fontWeight: FontWeight.w400,
                color: ThemeColors.color959595,
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
              "p": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
              "br": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          ),
          H(16),

          // Ticket info grid
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.access_time,
                  label: getLabel(key: 'created'),
                  value: _formatDate(ticket.createdTime),
                ),
              ),
              W(16),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.email_outlined,
                  label: getLabel(key: 'email'),
                  value: ticket.email,
                ),
              ),
            ],
          ),
          H(12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.phone_outlined,
                  label: getLabel(key: 'channel'),
                  value: ticket.channel,
                ),
              ),
              W(16),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.forum_outlined,
                  label: getLabel(key: 'threads'),
                  value: ticket.threadCount,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16.r,
              color: Colors.grey[600],
            ),
            W(4),
            Text(
              label,
              style: ts12w400c959595,
            ),
          ],
        ),
        H(4),
        Text(
          value,
          style: ts14w5c181818,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green.withValues(alpha: 0.1);
      case 'closed':
        return Colors.grey.withValues(alpha: 0.1);
      case 'pending':
        return Colors.orange.withValues(alpha: 0.1);
      case 'resolved':
        return Colors.blue.withValues(alpha: 0.1);
      default:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green[700]!;
      case 'closed':
        return Colors.grey[700]!;
      case 'pending':
        return Colors.orange[700]!;
      case 'resolved':
        return Colors.blue[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM dd, yyyy HH:mm').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
