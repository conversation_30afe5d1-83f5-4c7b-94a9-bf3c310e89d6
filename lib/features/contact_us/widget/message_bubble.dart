import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class MessageBubble extends StatelessWidget {
  final ConversationMessage message;

  const MessageBubble({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    // Determine if this is a customer message or support agent message
    // Based on the message type - 'thread' is typically customer, 'comment' is typically agent
    final isCustomerMessage = message.type == 'thread';

    return Row(
      mainAxisAlignment:
          isCustomerMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: [
        if (!isCustomerMessage) ...[
          // Support agent avatar
          CircleAvatar(
            radius: 16.r,
            backgroundColor: ThemeColors.color44322D,
            child: Icon(
              Icons.support_agent,
              size: 16.r,
              color: Colors.white,
            ),
          ),
          W(8),
        ],
        Flexible(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: isCustomerMessage
                  ? ThemeColors.color44322D
                  : ThemeColors.colorF8F8F8,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(18.r),
                topRight: Radius.circular(18.r),
                bottomLeft: isCustomerMessage
                    ? Radius.circular(18.r)
                    : Radius.circular(4.r),
                bottomRight: isCustomerMessage
                    ? Radius.circular(4.r)
                    : Radius.circular(18.r),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Author name (if not customer message)
                if (!isCustomerMessage) ...[
                  Text(
                    message.authorName,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.color44322D,
                    ),
                  ),
                  H(4),
                ],

                // Message content
                Html(
                  data: message.body,
                  style: {
                    "body": Style(
                      fontSize: FontSize(14.sp),
                      fontWeight: FontWeight.w400,
                      color: isCustomerMessage
                          ? Colors.white
                          : ThemeColors.color181818,
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),
                    "p": Style(
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),
                    "br": Style(
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),
                  },
                ),

                H(8),

                // Timestamp
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 12.r,
                      color:
                          isCustomerMessage ? Colors.white70 : Colors.grey[500],
                    ),
                    W(4),
                    Text(
                      _formatTime(message.createdTime),
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: isCustomerMessage
                            ? Colors.white70
                            : Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (isCustomerMessage) ...[
          W(8),
          // Customer avatar
          CircleAvatar(
            radius: 16.r,
            backgroundColor: ThemeColors.colorE1DDD2,
            child: Icon(
              Icons.person,
              size: 16.r,
              color: ThemeColors.color44322D,
            ),
          ),
        ],
      ],
    );
  }

  String _formatTime(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return DateFormat('MMM dd, HH:mm').format(date);
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return dateString;
    }
  }
}
