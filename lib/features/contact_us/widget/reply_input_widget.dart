import 'package:albalad_parking_app/features/contact_us/providers/ticket_details_provider.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/utils/text_style.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ReplyInputWidget extends HookConsumerWidget {
  final String ticketId;
  final String threadId;
  final bool isLoading;

  const ReplyInputWidget({
    super.key,
    required this.ticketId,
    required this.threadId,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final replyController = useTextEditingController();
    final focusNode = useFocusNode();
    final isButtonEnabled = useState(false);

    // Listen to text changes to enable/disable send button
    useEffect(() {
      void listener() {
        isButtonEnabled.value = replyController.text.trim().isNotEmpty;
      }

      replyController.addListener(listener);
      return () => replyController.removeListener(listener);
    }, [replyController]);

    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: ThemeColors.colorFFFFFF,
        border: Border(
          top: BorderSide(
            color: ThemeColors.colorEAEAEA,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Reply input field
            Container(
              decoration: BoxDecoration(
                color: ThemeColors.colorF8F8F8,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: focusNode.hasFocus
                      ? ThemeColors.color44322D
                      : ThemeColors.colorEAEAEA,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: replyController,
                      focusNode: focusNode,
                      enabled: !isLoading,
                      maxLines: null,
                      minLines: 1,
                      textInputAction: TextInputAction.newline,
                      decoration: InputDecoration(
                        hintText: getLabel(key: 'type_your_reply'),
                        hintStyle: ts14w4c959595,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 12.h,
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: ThemeColors.color181818,
                      ),
                    ),
                  ),

                  // Send button
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: GestureDetector(
                      onTap: (isButtonEnabled.value && !isLoading)
                          ? () => _sendReply(ref, replyController)
                          : null,
                      child: Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: (isButtonEnabled.value && !isLoading)
                              ? ThemeColors.color44322D
                              : Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                        child: isLoading
                            ? Padding(
                                padding: EdgeInsets.all(8.w),
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Icon(
                                Icons.send,
                                size: 20.r,
                                color: (isButtonEnabled.value && !isLoading)
                                    ? Colors.white
                                    : Colors.grey[500],
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Helper text
            if (!isLoading) ...[
              H(8),
              Text(
                getLabel(key: 'press_enter_for_new_line'),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _sendReply(WidgetRef ref, TextEditingController controller) {
    final content = controller.text.trim();
    if (content.isEmpty) return;

    // Post the reply
    ref.read(replyProvider.notifier).postReply(
          ticketId: ticketId,
          threadId: threadId,
          content: content,
        );

    // Clear the input field
    controller.clear();
  }
}
