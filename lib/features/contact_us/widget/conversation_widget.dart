import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/widget/thread_with_comments_widget.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConversationWidget extends StatelessWidget {
  final List<ConversationMessage> conversation;
  final String ticketId;

  const ConversationWidget({
    super.key,
    required this.conversation,
    required this.ticketId,
  });

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');
    if (conversation.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 64.r,
                color: Colors.grey[400],
              ),
              H(16),
              Text(
                getLabel(key: 'no_conversation_yet'),
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              H(8),
              Text(
                getLabel(key: 'start_conversation_by_sending_message'),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 24.h),
      child: Column(
        children: conversation.map((message) {
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: ThreadWithCommentsWidget(
              thread: message,
              ticketId: ticketId,
            ),
          );
        }).toList(),
      ),
    );
  }
}
