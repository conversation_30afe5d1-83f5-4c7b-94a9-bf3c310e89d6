import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/ticket_details_screen.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/utils/text_style.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TicketCard extends StatelessWidget {
  final Ticket ticket;

  const TicketCard({
    super.key,
    required this.ticket,
  });

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          TicketDetailsScreen.route,
          arguments: ticket.id,
        );
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: ThemeColors.colorFFFFFF,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: ThemeColors.colorEAEAEA,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with ticket number and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${getLabel(key: 'ticket')}${ticket.ticketNumber}',
                  style: ts16w6c181818,
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 4.h,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(ticket.status),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    ticket.status,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: _getStatusTextColor(ticket.status),
                    ),
                  ),
                ),
              ],
            ),
            H(12),

            // Subject
            Text(
              ticket.subject,
              style: ts14w5c181818,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            H(8),

            // Created date and channel
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16.r,
                  color: Colors.grey[600],
                ),
                W(4),
                Text(
                  _formatDate(ticket.createdTime),
                  style: ts12w400c959595,
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 6.w,
                    vertical: 2.h,
                  ),
                  decoration: BoxDecoration(
                    color: ThemeColors.colorF8F8F8,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    ticket.channel,
                    style: ts10c959595w4,
                  ),
                ),
              ],
            ),
            H(12),

            // Comments and threads info
            Row(
              children: [
                Icon(
                  Icons.comment_outlined,
                  size: 16.r,
                  color: Colors.grey[600],
                ),
                W(4),
                Text(
                  '${ticket.commentCount} ${getLabel(key: 'comments')}',
                  style: ts12w400c959595,
                ),
                W(16),
                Icon(
                  Icons.forum_outlined,
                  size: 16.r,
                  color: Colors.grey[600],
                ),
                W(4),
                Text(
                  '${ticket.threadCount} ${getLabel(key: 'threads')}',
                  style: ts12w400c959595,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green.withValues(alpha: 0.1);
      case 'closed':
        return Colors.grey.withValues(alpha: 0.1);
      case 'pending':
        return Colors.orange.withValues(alpha: 0.1);
      case 'resolved':
        return Colors.blue.withValues(alpha: 0.1);
      default:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green[700]!;
      case 'closed':
        return Colors.grey[700]!;
      case 'pending':
        return Colors.orange[700]!;
      case 'resolved':
        return Colors.blue[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
