import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class CommentBubble extends StatelessWidget {
  final ThreadComment comment;

  const CommentBubble({
    super.key,
    required this.comment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: ThemeColors.colorF8F8F8,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ThemeColors.colorEAEAEA,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timestamp only
          Row(
            children: [
              const Spacer(),
              Text(
                _formatTime(comment.createdTime),
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),

          H(8),

          // Comment content
          Html(
            data: comment.content,
            style: {
              "body": Style(
                fontSize: FontSize(13.sp),
                fontWeight: FontWeight.w400,
                color: ThemeColors.color181818,
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
              "p": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
              "br": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          ),

          // Public/Private indicator
          if (!comment.isPublic) ...[
            H(4),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                'Private',
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.orange[700],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return DateFormat('MMM dd, HH:mm').format(date);
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return dateString;
    }
  }
}
