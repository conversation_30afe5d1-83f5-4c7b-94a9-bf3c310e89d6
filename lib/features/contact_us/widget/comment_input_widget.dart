import 'package:albalad_parking_app/features/contact_us/providers/thread_comments_provider.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommentInputWidget extends HookConsumerWidget {
  final String ticketId;
  final String threadId;
  final bool isLoading;

  const CommentInputWidget({
    super.key,
    required this.ticketId,
    required this.threadId,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');

    final commentController = useTextEditingController();
    final focusNode = useFocusNode();
    final isButtonEnabled = useState(false);

    // Listen to text changes to enable/disable send button
    useEffect(() {
      void listener() {
        isButtonEnabled.value = commentController.text.trim().isNotEmpty;
      }

      commentController.addListener(listener);
      return () => commentController.removeListener(listener);
    }, [commentController]);

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: ThemeColors.colorFFFFFF,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ThemeColors.colorEAEAEA,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Comment input field
          Container(
            decoration: BoxDecoration(
              color: ThemeColors.colorF8F8F8,
              borderRadius: BorderRadius.circular(20.r),
              // border: Border.all(
              //   color: focusNode.hasFocus
              //       ? ThemeColors.color44322D
              //       : ThemeColors.colorEAEAEA,
              //   width: 1,
              // ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: commentController,
                    focusNode: focusNode,
                    enabled: !isLoading,
                    maxLines: null,
                    minLines: 1,
                    textInputAction: TextInputAction.newline,
                    decoration: InputDecoration(
                      hintText: getLabel(key: 'add_comment'),
                      hintStyle: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: ThemeColors.color959595,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 10.h,
                      ),
                    ),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: ThemeColors.color181818,
                    ),
                  ),
                ),

                // Send button
                Padding(
                  padding: EdgeInsets.only(right: 6.w),
                  child: GestureDetector(
                    onTap: (isButtonEnabled.value && !isLoading)
                        ? () => _postComment(ref, commentController)
                        : null,
                    child: Container(
                      width: 32.w,
                      height: 32.h,
                      decoration: BoxDecoration(
                        color: (isButtonEnabled.value && !isLoading)
                            ? ThemeColors.color44322D
                            : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: isLoading
                          ? Padding(
                              padding: EdgeInsets.all(6.w),
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Icon(
                              Icons.send,
                              size: 16.r,
                              color: (isButtonEnabled.value && !isLoading)
                                  ? Colors.white
                                  : Colors.grey[500],
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Helper text
          if (!isLoading) ...[
            H(6),
            Text(
              getLabel(key: 'comment_will_be_added_to_thread'),
              style: TextStyle(
                fontSize: 11.sp,
                color: Colors.grey[500],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _postComment(WidgetRef ref, TextEditingController controller) {
    final content = controller.text.trim();
    if (content.isEmpty) return;

    // Post the comment
    ref.read(postCommentProvider.notifier).postComment(
          ticketId: ticketId,
          threadId: threadId,
          content: content,
        );

    // Clear the input field
    controller.clear();
  }
}
