import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/providers/thread_comments_provider.dart';
import 'package:albalad_parking_app/features/contact_us/widget/comment_bubble.dart';
import 'package:albalad_parking_app/features/contact_us/widget/comment_input_widget.dart';
import 'package:albalad_parking_app/features/contact_us/widget/message_bubble.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/colors.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ThreadWithCommentsWidget extends ConsumerStatefulWidget {
  final ConversationMessage thread;
  final String ticketId;

  const ThreadWithCommentsWidget({
    super.key,
    required this.thread,
    required this.ticketId,
  });

  @override
  ConsumerState<ThreadWithCommentsWidget> createState() =>
      _ThreadWithCommentsWidgetState();
}

class _ThreadWithCommentsWidgetState
    extends ConsumerState<ThreadWithCommentsWidget> {
  bool _isExpanded = false;
  bool _commentsLoaded = false;
  bool _isLoadingComments = false;

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');

    final threadCommentsState = ref.watch(threadCommentsProvider);
    final postCommentState = ref.watch(postCommentProvider);

    // Get comments for this specific thread
    final threadComments = threadCommentsState.value?[widget.thread.threadId];

    // Listen to comment posting state changes
    ref.listen<AsyncValue<ReplyResponse?>>(postCommentProvider,
        (previous, next) {
      next.whenOrNull(
        data: (replyResponse) {
          if (replyResponse != null) {
            // Comment was posted successfully, refresh comments
            ref.read(threadCommentsProvider.notifier).refreshThreadComments(
                  ticketId: widget.ticketId,
                  threadId: widget.thread.threadId,
                );
            // Reset comment posting state
            ref.read(postCommentProvider.notifier).resetState();
          }
        },
      );
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Original thread message
        MessageBubble(message: widget.thread),

        H(8),

        // Comments section
        Container(
          margin: EdgeInsets.only(left: 32.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Toggle comments button
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });

                  // Load comments when expanding for the first time
                  if (_isExpanded && !_commentsLoaded) {
                    setState(() {
                      _isLoadingComments = true;
                    });
                    ref
                        .read(threadCommentsProvider.notifier)
                        .fetchThreadComments(
                          ticketId: widget.ticketId,
                          threadId: widget.thread.threadId,
                        )
                        .then((_) {
                      if (mounted) {
                        setState(() {
                          _isLoadingComments = false;
                          _commentsLoaded = true;
                        });
                      }
                    });
                  }
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: ThemeColors.colorF8F8F8,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: ThemeColors.colorEAEAEA,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                        size: 16.r,
                        color: ThemeColors.color44322D,
                      ),
                      W(4),
                      Text(
                        _isExpanded
                            ? getLabel(key: 'hide_comments')
                            : '${getLabel(key: 'show_comments')} (${threadComments?.totalComments ?? 0})',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: ThemeColors.color44322D,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Comments list and input (shown when expanded)
              if (_isExpanded) ...[
                H(12),

                // Comments list or loading
                if (_isLoadingComments) ...[
                  // Loading indicator
                  Container(
                    padding: EdgeInsets.all(16.w),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 16.w,
                          height: 16.h,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ThemeColors.color44322D,
                            ),
                          ),
                        ),
                        W(12),
                        Text(
                          getLabel(key: 'loading_comments'),
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else if (threadComments != null) ...[
                  if (threadComments.comments.isEmpty)
                    Container(
                      padding: EdgeInsets.all(16.w),
                      child: Text(
                        getLabel(key: 'no_comments_yet'),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    )
                  else
                    ...threadComments.comments.map((comment) => Padding(
                          padding: EdgeInsets.only(bottom: 8.h),
                          child: CommentBubble(comment: comment),
                        )),
                ],

                H(8),

                // Comment input
                CommentInputWidget(
                  ticketId: widget.ticketId,
                  threadId: widget.thread.threadId,
                  isLoading: postCommentState.isLoading,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
