import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/providers/support_ticket_provider.dart';
import 'package:albalad_parking_app/features/contact_us/repository/ticket_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A StateNotifier to manage the state of thread comments.
class ThreadCommentsNotifier extends StateNotifier<AsyncValue<Map<String, ThreadCommentsResponse>>> {
  final TicketRepository _ticketRepository;

  ThreadCommentsNotifier(this._ticketRepository)
      : super(const AsyncValue.data({}));

  /// Fetches comments for a specific thread and updates the state accordingly.
  Future<void> fetchThreadComments({
    required String ticketId,
    required String threadId,
  }) async {
    try {
      // Get current state
      final currentState = state.value ?? {};
      
      // Set loading state for this specific thread
      state = AsyncValue.data({
        ...currentState,
        threadId: ThreadCommentsResponse(
          success: true,
          comments: [],
          totalComments: 0,
          threadId: threadId,
          ticketId: ticketId,
        ),
      });

      // Call the repository to fetch thread comments
      final response = await _ticketRepository.getThreadComments(
        ticketId: ticketId,
        threadId: threadId,
      );

      // Update the state with the success response
      state = AsyncValue.data({
        ...currentState,
        threadId: response,
      });
    } catch (e, st) {
      // If an error occurs, update the state with the error
      state = AsyncValue.error(e, st);
    }
  }

  /// Refreshes comments for a specific thread
  Future<void> refreshThreadComments({
    required String ticketId,
    required String threadId,
  }) async {
    await fetchThreadComments(ticketId: ticketId, threadId: threadId);
  }

  /// Gets comments for a specific thread from the current state
  ThreadCommentsResponse? getCommentsForThread(String threadId) {
    final currentState = state.value;
    if (currentState == null) return null;
    return currentState[threadId];
  }

  /// Clears all thread comments from state
  void clearComments() {
    state = const AsyncValue.data({});
  }
}

/// A StateNotifier to manage the state of posting comments to threads.
class PostCommentNotifier extends StateNotifier<AsyncValue<ReplyResponse?>> {
  final TicketRepository _ticketRepository;

  PostCommentNotifier(this._ticketRepository)
      : super(const AsyncValue.data(null));

  /// Posts a comment to a thread and updates the state accordingly.
  Future<void> postComment({
    required String ticketId,
    required String threadId,
    required String content,
  }) async {
    // Set state to loading.
    state = const AsyncValue.loading();
    try {
      // Call the repository to post the comment.
      final response = await _ticketRepository.replyToThread(
        ticketId: ticketId,
        threadId: threadId,
        content: content,
      );
      // If successful, update the state with the success response.
      state = AsyncValue.data(response);
    } catch (e, st) {
      // If an error occurs, update the state with the error.
      state = AsyncValue.error(e, st);
    }
  }

  /// Resets the comment posting state
  void resetState() {
    state = const AsyncValue.data(null);
  }
}

/// Riverpod provider for the ThreadCommentsNotifier.
final threadCommentsProvider = StateNotifierProvider<ThreadCommentsNotifier, AsyncValue<Map<String, ThreadCommentsResponse>>>((ref) {
  // Get the TicketRepository from its provider.
  final ticketRepository = ref.watch(ticketRepositoryProvider);
  return ThreadCommentsNotifier(ticketRepository);
});

/// Riverpod provider for the PostCommentNotifier.
final postCommentProvider = StateNotifierProvider<PostCommentNotifier, AsyncValue<ReplyResponse?>>((ref) {
  // Get the TicketRepository from its provider.
  final ticketRepository = ref.watch(ticketRepositoryProvider);
  return PostCommentNotifier(ticketRepository);
});
