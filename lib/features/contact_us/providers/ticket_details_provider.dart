import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/providers/support_ticket_provider.dart';
import 'package:albalad_parking_app/features/contact_us/repository/ticket_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A StateNotifier to manage the state of ticket details and replies.
class TicketDetailsNotifier
    extends StateNotifier<AsyncValue<TicketDetailsResponse?>> {
  final TicketRepository _ticketRepository;

  TicketDetailsNotifier(this._ticketRepository)
      : super(const AsyncValue.data(null));

  /// Fetches ticket details by ID and updates the state accordingly.
  Future<void> fetchTicketDetails({required String ticketId}) async {
    // Set state to loading.
    state = const AsyncValue.loading();
    try {
      // Call the repository to fetch ticket details.
      final response =
          await _ticketRepository.getTicketDetails(ticketId: ticketId);
      // If successful, update the state with the success response.
      state = AsyncValue.data(response);
    } catch (e, st) {
      // If an error occurs, update the state with the error.
      state = AsyncValue.error(e, st);
    }
  }

  /// Refreshes the ticket details
  Future<void> refreshTicketDetails({required String ticketId}) async {
    await fetchTicketDetails(ticketId: ticketId);
  }
}

/// A StateNotifier to manage the state of reply posting.
class ReplyNotifier extends StateNotifier<AsyncValue<ReplyResponse?>> {
  final TicketRepository _ticketRepository;

  ReplyNotifier(this._ticketRepository) : super(const AsyncValue.data(null));

  /// Posts a reply to a thread and updates the state accordingly.
  Future<void> postReply({
    required String ticketId,
    required String threadId,
    required String content,
  }) async {
    // Set state to loading.
    state = const AsyncValue.loading();
    try {
      // Call the repository to post the reply.
      final response = await _ticketRepository.replyToThread(
        ticketId: ticketId,
        threadId: threadId,
        content: content,
      );
      // If successful, update the state with the success response.
      state = AsyncValue.data(response);
    } catch (e, st) {
      // If an error occurs, update the state with the error.
      state = AsyncValue.error(e, st);
    }
  }

  /// Resets the reply state
  void resetState() {
    state = const AsyncValue.data(null);
  }
}

/// Riverpod provider for the TicketDetailsNotifier.
final ticketDetailsProvider = StateNotifierProvider<TicketDetailsNotifier,
    AsyncValue<TicketDetailsResponse?>>((ref) {
  // Get the TicketRepository from its provider.
  final ticketRepository = ref.watch(ticketRepositoryProvider);
  return TicketDetailsNotifier(ticketRepository);
});

/// Riverpod provider for the ReplyNotifier.
final replyProvider =
    StateNotifierProvider<ReplyNotifier, AsyncValue<ReplyResponse?>>((ref) {
  // Get the TicketRepository from its provider.
  final ticketRepository = ref.watch(ticketRepositoryProvider);
  return ReplyNotifier(ticketRepository);
});
