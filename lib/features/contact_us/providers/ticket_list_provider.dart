import 'package:albalad_parking_app/features/contact_us/models/support_ticket_response.dart';
import 'package:albalad_parking_app/features/contact_us/repository/ticket_repository.dart';
import 'package:albalad_parking_app/features/contact_us/providers/support_ticket_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A StateNotifier to manage the state of the ticket listing process.
/// It holds the asynchronous state: loading, data (success), or error.
class TicketListNotifier
    extends StateNotifier<AsyncValue<TicketListResponse?>> {
  final TicketRepository _ticketRepository;

  TicketListNotifier(this._ticketRepository)
      : super(const AsyncValue.data(null));

  /// Fetches the list of tickets and updates the state accordingly.
  Future<void> fetchTickets() async {
    // Set state to loading.
    state = const AsyncValue.loading();
    try {
      // Call the repository to fetch tickets.
      final response = await _ticketRepository.getTickets();
      // If successful, update the state with the success response.
      state = AsyncValue.data(response);
    } catch (e, st) {
      // If an error occurs, update the state with the error.
      state = AsyncValue.error(e, st);
    }
  }

  /// Refreshes the ticket list
  Future<void> refreshTickets() async {
    await fetchTickets();
  }
}

/// Riverpod provider for the TicketListNotifier.
final ticketListProvider =
    StateNotifierProvider<TicketListNotifier, AsyncValue<TicketListResponse?>>(
        (ref) {
  // Get the TicketRepository from its provider.
  final ticketRepository = ref.watch(ticketRepositoryProvider);
  return TicketListNotifier(ticketRepository);
});
