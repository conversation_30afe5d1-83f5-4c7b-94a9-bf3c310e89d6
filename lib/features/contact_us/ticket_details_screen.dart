import 'package:albalad_parking_app/features/contact_us/providers/ticket_details_provider.dart';
import 'package:albalad_parking_app/features/contact_us/widget/conversation_widget.dart';
import 'package:albalad_parking_app/features/contact_us/widget/ticket_details_header.dart';
import 'package:albalad_parking_app/features/home/<USER>/label_provider.dart';
import 'package:albalad_parking_app/utils/custom_appbar.dart';
import 'package:albalad_parking_app/widgets/custom_gradient_spinner.dart';
import 'package:albalad_parking_app/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TicketDetailsScreen extends ConsumerStatefulWidget {
  static const route = '/ticket_details_screen';

  final String ticketId;

  const TicketDetailsScreen({
    super.key,
    required this.ticketId,
  });

  @override
  ConsumerState<TicketDetailsScreen> createState() =>
      _TicketDetailsScreenState();
}

class _TicketDetailsScreenState extends ConsumerState<TicketDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch ticket details when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(ticketDetailsProvider.notifier)
          .fetchTicketDetails(ticketId: widget.ticketId);
    });
  }

  @override
  Widget build(BuildContext context) {
    String getLabel({required String key}) =>
        translateLabelFunction(key: key, screen: 'common_heading');

    final ticketDetailsState = ref.watch(ticketDetailsProvider);

    return Scaffold(
      appBar: albaladAppBar(
        context: context,
        title: getLabel(key: 'ticket_details'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref
              .read(ticketDetailsProvider.notifier)
              .refreshTicketDetails(ticketId: widget.ticketId);
        },
        child: ticketDetailsState.when(
          loading: () => const Center(
            child: CustomGradientSpinner(),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64.r,
                    color: Colors.red,
                  ),
                  H(16),
                  Text(
                    getLabel(key: 'something_went_wrong'),
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  H(8),
                  Text(
                    error.toString(),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  H(24),
                  ElevatedButton(
                    onPressed: () {
                      ref
                          .read(ticketDetailsProvider.notifier)
                          .fetchTicketDetails(ticketId: widget.ticketId);
                    },
                    child: Text(getLabel(key: 'retry')),
                  ),
                ],
              ),
            ),
          ),
          data: (ticketDetailsResponse) {
            if (ticketDetailsResponse == null) {
              return const Center(
                child: CustomGradientSpinner(),
              );
            }

            final ticket = ticketDetailsResponse.ticket;
            final conversation = ticketDetailsResponse.conversation;

            return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                children: [
                  // Ticket details header
                  TicketDetailsHeader(ticket: ticket),

                  // Conversation list with comments
                  ConversationWidget(
                    conversation: conversation,
                    ticketId: widget.ticketId,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
