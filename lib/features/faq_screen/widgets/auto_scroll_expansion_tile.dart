/// Custom ExpansionTile widget with auto-scroll functionality
/// This widget automatically scrolls to show expanded content when the tile is at the bottom of the screen

import 'package:flutter/material.dart';

class AutoScrollExpansionTile extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final ScrollController? scrollController;
  final int index;
  final GlobalKey? itemKey;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? childrenPadding;
  final Color? iconColor;
  final ShapeBorder? shape;
  final ShapeBorder? collapsedShape;
  final ValueChanged<bool>? onExpansionChanged;

  const AutoScrollExpansionTile({
    super.key,
    required this.title,
    required this.children,
    required this.index,
    this.scrollController,
    this.itemKey,
    this.backgroundColor,
    this.childrenPadding,
    this.iconColor,
    this.shape,
    this.collapsedShape,
    this.onExpansionChanged,
  });

  @override
  State<AutoScrollExpansionTile> createState() => _AutoScrollExpansionTileState();
}

class _AutoScrollExpansionTileState extends State<AutoScrollExpansionTile> {
  bool _isExpanded = false;

  void _handleExpansionChanged(bool isExpanded) {
    setState(() {
      _isExpanded = isExpanded;
    });

    // Call the original callback if provided
    widget.onExpansionChanged?.call(isExpanded);

    // Handle auto-scroll when expanding
    if (isExpanded && widget.scrollController != null) {
      _scrollToShowExpandedContent();
    }
  }

  void _scrollToShowExpandedContent() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final scrollController = widget.scrollController!;
      
      if (!scrollController.hasClients) return;

      // Get the render box of the current item
      final RenderBox? renderBox = widget.itemKey?.currentContext?.findRenderObject() as RenderBox?;
      
      if (renderBox == null) return;

      try {
        // Get the position of the item relative to the screen
        final Offset itemPosition = renderBox.localToGlobal(Offset.zero);
        final double itemTop = itemPosition.dy;
        final double itemHeight = renderBox.size.height;
        final double itemBottom = itemTop + itemHeight;
        
        // Get screen and viewport dimensions
        final double screenHeight = MediaQuery.of(context).size.height;
        final double viewportHeight = scrollController.position.viewportDimension;
        
        // Calculate the visible area (excluding app bar and other UI elements)
        final double appBarHeight = AppBar().preferredSize.height + MediaQuery.of(context).padding.top;
        final double availableHeight = screenHeight - appBarHeight;
        
        // Check if the item (especially when expanded) will be cut off at the bottom
        final bool needsScrolling = itemBottom > (availableHeight * 0.8);
        
        if (needsScrolling) {
          // Calculate the target scroll position
          final double currentScrollOffset = scrollController.offset;
          
          // We want to position the item so that it's visible with some padding
          final double targetItemPosition = availableHeight * 0.3; // 30% from top of available area
          final double scrollAdjustment = itemTop - appBarHeight - targetItemPosition;
          final double targetScrollPosition = currentScrollOffset + scrollAdjustment;
          
          // Ensure we don't scroll beyond the limits
          final double clampedScrollPosition = targetScrollPosition.clamp(
            0.0,
            scrollController.position.maxScrollExtent,
          );
          
          // Animate to the calculated position
          scrollController.animateTo(
            clampedScrollPosition,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut,
          );
        }
      } catch (e) {
        // Handle any errors gracefully - maybe the widget was disposed
        debugPrint('Error during auto-scroll: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: widget.title,
      backgroundColor: widget.backgroundColor,
      childrenPadding: widget.childrenPadding,
      iconColor: widget.iconColor,
      shape: widget.shape,
      collapsedShape: widget.collapsedShape,
      onExpansionChanged: _handleExpansionChanged,
      children: widget.children,
    );
  }
}

/// Extension to make it easier to use AutoScrollExpansionTile
extension AutoScrollExpansionTileHelper on Widget {
  /// Wraps any widget with auto-scroll functionality when used as ExpansionTile title
  Widget withAutoScroll({
    required List<Widget> children,
    required int index,
    ScrollController? scrollController,
    GlobalKey? itemKey,
    Color? backgroundColor,
    EdgeInsetsGeometry? childrenPadding,
    Color? iconColor,
    ShapeBorder? shape,
    ShapeBorder? collapsedShape,
    ValueChanged<bool>? onExpansionChanged,
  }) {
    return AutoScrollExpansionTile(
      title: this,
      children: children,
      index: index,
      scrollController: scrollController,
      itemKey: itemKey,
      backgroundColor: backgroundColor,
      childrenPadding: childrenPadding,
      iconColor: iconColor,
      shape: shape,
      collapsedShape: collapsedShape,
      onExpansionChanged: onExpansionChanged,
    );
  }
}
