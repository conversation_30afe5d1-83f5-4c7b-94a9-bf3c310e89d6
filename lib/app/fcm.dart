import 'dart:developer';

import 'package:albalad_parking_app/app/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:flutter/material.dart';

class FCM {
  static FirebaseMessaging messaging = FirebaseMessaging.instance;

  static Future<String?> token() async {
    return await messaging.getToken();
  }

  static subscribeEnglishTopic() async {
    await messaging.unsubscribeFromTopic('customer_ar');
    await messaging.subscribeToTopic("customer_en");
  }

  static subscribeArabicTopic() async {
    await messaging.unsubscribeFromTopic('customer_en');
    await messaging.subscribeToTopic("customer_ar");
  }

  static Future<void> storeFCMToken(String fcm) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('fcm', fcm);
  }

  //---------------------------------------------Dio--------------------------------------------------------//
  Dio dio = DioClient().dio;
  //---------------------------------------------APIs--------------------------------------------------------//
  // Future<void> refreshToken({required BuildContext context}) async {
  //   try {
  //     SharedPreferences prefs = await SharedPreferences.getInstance();
  //     String? currentToken = prefs.getString('fcm');
  //     String? newToken = await token();

  //     if (newToken != null && currentToken != newToken) {
  //       Response response = await dio.get(
  //         'refresh_fcm_token',
  //         options: Options(
  //             headers: await Api.getAuthorizationHeader(),
  //             validateStatus: (status) => RefreshToken.validateStatus(
  //                 context: context, status: status!)),
  //       );
  //       log('status code: ${response.statusCode}');
  //       log('body: ${response.data}');
  //       prefs.setString('fcm', newToken);
  //     }
  //   } catch (e) {
  //     debugPrint('$e');
  //   }
  // }

  static Future<void> requestPermission() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('User granted provisional permission');
    } else {
      debugPrint('User declined or has not accepted permission');
    }
    // debugPrint('User granted permission: ${settings.authorizationStatus} fcm');
  }

  static init() async {
    await requestPermission();
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    log('FCM - ${await FirebaseMessaging.instance.getToken()}');
  }

  @pragma('vm:entry-point')
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    // If you're going to use other Firebase services in the background, such as Firestore,
    // make sure you call `initializeApp` before using other Firebase services.
    await Firebase.initializeApp();
  }
}
