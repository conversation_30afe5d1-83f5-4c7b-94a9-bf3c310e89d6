import 'dart:developer';
import 'package:albalad_parking_app/app/app.dart';
import 'package:albalad_parking_app/features/authentication/services/token_refresh_service.dart';
import 'package:albalad_parking_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_parking_app/helper/secure_storage_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthInterceptor extends Interceptor {
  bool _isNavigating = false;
  final TokenRefreshService _tokenRefreshService = TokenRefreshService();

  AuthInterceptor() {
    // Initialize the token refresh service
    _tokenRefreshService.initialize();
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // print('Response: ${response.statusCode}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final response = err.response;

    if (response?.statusCode == 401) {
      if (kDebugMode) {
        log('401 Unauthorized detected, attempting token refresh',
            name: 'AuthInterceptor');
      }

      // Try to refresh the token
      final newAccessToken = await _tokenRefreshService.refreshToken();

      if (newAccessToken != null) {
        if (kDebugMode) {
          log('Token refreshed successfully, retrying original request',
              name: 'AuthInterceptor');
        }

        // Update the original request with the new token
        final originalRequest = err.requestOptions;
        originalRequest.headers['Authorization'] = 'Bearer $newAccessToken';

        try {
          // Retry the original request with the new token
          final dio = Dio();
          final retryResponse = await dio.fetch(originalRequest);
          return handler.resolve(retryResponse);
        } catch (retryError) {
          if (kDebugMode) {
            log('Retry request failed: $retryError', name: 'AuthInterceptor');
          }
          // If retry fails, proceed with logout
          await _handleLogout();
          return handler.next(err);
        }
      } else {
        if (kDebugMode) {
          log('Token refresh failed, redirecting to login',
              name: 'AuthInterceptor');
        }
        // Token refresh failed, redirect to login
        await _handleLogout();
      }
    }

    super.onError(err, handler);
  }

  /// Handle logout by clearing data and navigating to sign-in screen
  Future<void> _handleLogout() async {
    if (_isNavigating) return;

    _isNavigating = true;

    try {
      // Clear authentication data
      await _clearAuthData();

      // Navigate to sign-in screen
      navigatorKey.currentState?.pushNamedAndRemoveUntil(
        SiginScreen.route,
        (route) => false,
      );
    } catch (e) {
      if (kDebugMode) {
        log('Error during logout: $e', name: 'AuthInterceptor');
      }
    } finally {
      _isNavigating = false;
    }
  }

  /// Clear stored authentication data
  Future<void> _clearAuthData() async {
    try {
      // Clear tokens from memory
      Token.accessToken = null;
      Token.refreshToken = null;
      Token.expiresIn = null;

      // Clear secure storage
      await SecureStorageHelper.instance.clearAll();

      // Clear shared preferences (preserve app settings)
      final prefs = await SharedPreferences.getInstance();
      final isOnboardEnabled = prefs.getBool('is_onboard_enabled') ?? true;
      await prefs.clear();
      await prefs.setBool('is_onboard_enabled', isOnboardEnabled);

      // Clear token refresh service state
      _tokenRefreshService.clearRefreshState();

      if (kDebugMode) {
        log('Authentication data cleared successfully',
            name: 'AuthInterceptor');
      }
    } catch (e) {
      if (kDebugMode) {
        log('Error clearing auth data: $e', name: 'AuthInterceptor');
      }
    }
  }
}
