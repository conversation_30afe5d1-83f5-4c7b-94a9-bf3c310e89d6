import 'package:albalad_parking_app/helper/dialog_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Centralized error handling utility for the Al-Balad parking app
/// Provides consistent error handling patterns across the application
class ErrorHandler {
  /// Handle DioException with consistent error messages and logging
  static AppError handleDioException(DioException error, {String? context}) {
    if (kDebugMode) {
      debugPrint('🔥 DioException in ${context ?? 'Unknown'}: ${error.type}');
      debugPrint('🔥 Error message: ${error.message}');
      debugPrint('🔥 Response data: ${error.response?.data}');
      debugPrint('🔥 Stack trace: ${error.stackTrace}');
    }

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return AppError(
          type: ErrorType.network,
          message: 'Connection timeout. Please check your internet connection.',
          originalError: error,
        );
      case DioExceptionType.sendTimeout:
        return AppError(
          type: ErrorType.network,
          message: 'Request timeout. Please try again.',
          originalError: error,
        );
      case DioExceptionType.receiveTimeout:
        return AppError(
          type: ErrorType.network,
          message: 'Server response timeout. Please try again.',
          originalError: error,
        );
      case DioExceptionType.badResponse:
        return _handleBadResponse(error);
      case DioExceptionType.cancel:
        return AppError(
          type: ErrorType.cancelled,
          message: 'Request was cancelled',
          originalError: error,
        );
      case DioExceptionType.connectionError:
        return AppError(
          type: ErrorType.network,
          message: 'No internet connection. Please check your network.',
          originalError: error,
        );
      case DioExceptionType.badCertificate:
        return AppError(
          type: ErrorType.security,
          message: 'Security certificate error. Please contact support.',
          originalError: error,
        );
      case DioExceptionType.unknown:
        return AppError(
          type: ErrorType.unknown,
          message: 'An unexpected error occurred. Please try again.',
          originalError: error,
        );
    }
  }

  /// Handle bad response from server
  static AppError _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return AppError(
          type: ErrorType.validation,
          message: _extractErrorMessage(responseData) ?? 'Invalid request data',
          originalError: error,
        );
      case 401:
        return AppError(
          type: ErrorType.authentication,
          message: 'Session expired. Please login again.',
          originalError: error,
        );
      case 403:
        return AppError(
          type: ErrorType.authorization,
          message: 'Access denied. You don\'t have permission for this action.',
          originalError: error,
        );
      case 404:
        return AppError(
          type: ErrorType.notFound,
          message: 'Requested resource not found.',
          originalError: error,
        );
      case 422:
        return AppError(
          type: ErrorType.validation,
          message: _extractErrorMessage(responseData) ?? 'Validation failed',
          originalError: error,
        );
      case 429:
        return AppError(
          type: ErrorType.rateLimited,
          message: 'Too many requests. Please try again later.',
          originalError: error,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return AppError(
          type: ErrorType.server,
          message: 'Server error. Please try again later.',
          originalError: error,
        );
      default:
        return AppError(
          type: ErrorType.unknown,
          message: _extractErrorMessage(responseData) ?? 'An error occurred',
          originalError: error,
        );
    }
  }

  /// Extract error message from response data
  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;

    try {
      if (responseData is Map<String, dynamic>) {
        // Check for common error message fields
        if (responseData.containsKey('message')) {
          return responseData['message']?.toString();
        }
        if (responseData.containsKey('error')) {
          return responseData['error']?.toString();
        }
        if (responseData.containsKey('errors')) {
          final errors = responseData['errors'];
          if (errors is Map<String, dynamic> && errors.isNotEmpty) {
            return errors.values.first?.toString();
          }
          if (errors is List && errors.isNotEmpty) {
            return errors.first?.toString();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to extract error message: $e');
      }
    }

    return null;
  }

  /// Handle generic exceptions
  static AppError handleGenericException(Object error, {String? context}) {
    if (kDebugMode) {
      debugPrint('🔥 Generic Exception in ${context ?? 'Unknown'}: $error');
    }

    if (error is DioException) {
      return handleDioException(error, context: context);
    }

    return AppError(
      type: ErrorType.unknown,
      message: 'An unexpected error occurred. Please try again.',
      originalError: error,
    );
  }

  /// Show error dialog to user
  static void showErrorDialog(BuildContext context, AppError error) {
    if (!context.mounted) return;

    // Don't show dialog for cancelled requests
    if (error.type == ErrorType.cancelled) return;

    DialogHelper.showErrorBottomSheet(
      context: context,
      message: error.message,
    );
  }

  /// Log error for debugging and analytics
  static void logError(AppError error, {String? context}) {
    if (kDebugMode) {
      debugPrint('📝 Logging error in ${context ?? 'Unknown'}:');
      debugPrint('   Type: ${error.type}');
      debugPrint('   Message: ${error.message}');
      debugPrint('   Original: ${error.originalError}');
    }

    // TODO: Send to analytics/crashlytics in production
    // FirebaseCrashlytics.instance.recordError(
    //   error.originalError,
    //   null,
    //   fatal: false,
    // );
  }
}

/// Represents different types of errors that can occur in the app
enum ErrorType {
  network,
  authentication,
  authorization,
  validation,
  notFound,
  server,
  rateLimited,
  security,
  cancelled,
  unknown,
}

/// Represents an application error with type and message
class AppError {
  final ErrorType type;
  final String message;
  final Object? originalError;

  const AppError({
    required this.type,
    required this.message,
    this.originalError,
  });

  @override
  String toString() {
    return 'AppError(type: $type, message: $message, originalError: $originalError)';
  }

  /// Check if this is a network-related error
  bool get isNetworkError => type == ErrorType.network;

  /// Check if this is an authentication error
  bool get isAuthError => type == ErrorType.authentication;

  /// Check if this is a validation error
  bool get isValidationError => type == ErrorType.validation;

  /// Check if this is a server error
  bool get isServerError => type == ErrorType.server;
}
