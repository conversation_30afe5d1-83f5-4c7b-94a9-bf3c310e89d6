import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// No description provided for @splashScreen.
  ///
  /// In en, this message translates to:
  /// **'Splash Screen!'**
  String get splashScreen;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @weAreHappyToSeeYouAgain.
  ///
  /// In en, this message translates to:
  /// **'We are happy to see you again.'**
  String get weAreHappyToSeeYouAgain;

  /// No description provided for @toUseYourAccountYouShouldLoginFirst.
  ///
  /// In en, this message translates to:
  /// **'To use your account you should login first.'**
  String get toUseYourAccountYouShouldLoginFirst;

  /// No description provided for @mobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Mobile Number'**
  String get mobileNumber;

  /// No description provided for @enterYourMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Mobile Number'**
  String get enterYourMobileNumber;

  /// No description provided for @enterYourMobileNumberValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter your mobile number'**
  String get enterYourMobileNumberValidation;

  /// No description provided for @enterAValidMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid mobile number'**
  String get enterAValidMobileNumber;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome !'**
  String get welcome;

  /// No description provided for @support.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// No description provided for @verifyCode.
  ///
  /// In en, this message translates to:
  /// **'Verify Code'**
  String get verifyCode;

  /// No description provided for @enterThe4DigitCodeSentToYouAt.
  ///
  /// In en, this message translates to:
  /// **'Enter the 4 digit code sent to you at'**
  String get enterThe4DigitCodeSentToYouAt;

  /// No description provided for @verify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verify;

  /// No description provided for @parkingNearby.
  ///
  /// In en, this message translates to:
  /// **'Parking nearby'**
  String get parkingNearby;

  /// No description provided for @searchLocation.
  ///
  /// In en, this message translates to:
  /// **'Search Location'**
  String get searchLocation;

  /// No description provided for @searchCountry.
  ///
  /// In en, this message translates to:
  /// **'Search Country'**
  String get searchCountry;

  /// No description provided for @dontHaveAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Don’t have an account? '**
  String get dontHaveAnAccount;

  /// No description provided for @signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @individual.
  ///
  /// In en, this message translates to:
  /// **'Individual'**
  String get individual;

  /// No description provided for @corporate.
  ///
  /// In en, this message translates to:
  /// **'Corporate'**
  String get corporate;

  /// No description provided for @individual_signup_title.
  ///
  /// In en, this message translates to:
  /// **'Create an account to get started\nand enjoy the full experience with us.'**
  String get individual_signup_title;

  /// No description provided for @corporate_signup_title.
  ///
  /// In en, this message translates to:
  /// **'Register your business to access extra\nfeatures and benefits'**
  String get corporate_signup_title;

  /// No description provided for @full_name.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get full_name;

  /// No description provided for @enter_your_name.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Name'**
  String get enter_your_name;

  /// No description provided for @mobile_number.
  ///
  /// In en, this message translates to:
  /// **'Mobile Number'**
  String get mobile_number;

  /// No description provided for @email_address.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get email_address;

  /// No description provided for @enter_your_email.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Email'**
  String get enter_your_email;

  /// No description provided for @i_agree_to_the.
  ///
  /// In en, this message translates to:
  /// **'I agree to the'**
  String get i_agree_to_the;

  /// No description provided for @terms_and_conditions.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get terms_and_conditions;

  /// No description provided for @already_have_an_account.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get already_have_an_account;

  /// No description provided for @company_name.
  ///
  /// In en, this message translates to:
  /// **'Company Name'**
  String get company_name;

  /// No description provided for @enter_your_company_name.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Company Name'**
  String get enter_your_company_name;

  /// No description provided for @thisFieldIsRequired.
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get thisFieldIsRequired;

  /// No description provided for @enterAValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid email'**
  String get enterAValidEmail;

  /// No description provided for @chooseYourPreferredLanguage.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Preferred Language'**
  String get chooseYourPreferredLanguage;

  /// No description provided for @continueString.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueString;

  /// No description provided for @available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// No description provided for @terms_conditions.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get terms_conditions;

  /// No description provided for @myProfile.
  ///
  /// In en, this message translates to:
  /// **'My Profile'**
  String get myProfile;

  /// No description provided for @myVehicles.
  ///
  /// In en, this message translates to:
  /// **'My Vehicles'**
  String get myVehicles;

  /// No description provided for @vehicles.
  ///
  /// In en, this message translates to:
  /// **'Vehicles'**
  String get vehicles;

  /// No description provided for @wallet.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get wallet;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @privacyPolicies.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policies'**
  String get privacyPolicies;

  /// No description provided for @notificationSettings.
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettings;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @logOut.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logOut;

  /// No description provided for @editProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// No description provided for @deleteMyAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete My Account'**
  String get deleteMyAccount;

  /// No description provided for @saveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// No description provided for @verifyNumber.
  ///
  /// In en, this message translates to:
  /// **'Verify Number'**
  String get verifyNumber;

  /// No description provided for @verifyNewPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Verify New Phone Number'**
  String get verifyNewPhoneNumber;

  /// No description provided for @profileOtpDescription.
  ///
  /// In en, this message translates to:
  /// **'We’ve sent a one-time password (OTP) to your new phone number. Please enter it below to confirm the change'**
  String get profileOtpDescription;

  /// No description provided for @uploadImageForm.
  ///
  /// In en, this message translates to:
  /// **'Upload Image Form'**
  String get uploadImageForm;

  /// No description provided for @deleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// No description provided for @whyWouldYouWantToDeleteYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Why would you want to delete your account ?'**
  String get whyWouldYouWantToDeleteYourAccount;

  /// No description provided for @noLongerUseThisService.
  ///
  /// In en, this message translates to:
  /// **'No longer use this service.'**
  String get noLongerUseThisService;

  /// No description provided for @yourFeedbackHelpUsToImprove.
  ///
  /// In en, this message translates to:
  /// **'Your feedback help us to improve'**
  String get yourFeedbackHelpUsToImprove;

  /// No description provided for @shareYourFeedback.
  ///
  /// In en, this message translates to:
  /// **'Share your feedback'**
  String get shareYourFeedback;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @deleteYourAccountWill.
  ///
  /// In en, this message translates to:
  /// **'Deleting your account will permanently remove all your data, active subscriptions, parking history & profile details. This action cannot be undone.'**
  String get deleteYourAccountWill;

  /// No description provided for @pleaseshareyourfeedback.
  ///
  /// In en, this message translates to:
  /// **'Please share your feedback'**
  String get pleaseshareyourfeedback;

  /// No description provided for @confirmYourAction.
  ///
  /// In en, this message translates to:
  /// **'Confirm Your Action'**
  String get confirmYourAction;

  /// No description provided for @deleteDialoge.
  ///
  /// In en, this message translates to:
  /// **'To proceed with deleting your account, please enter the one-time password (OTP) sent to your registered mobile number.'**
  String get deleteDialoge;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @addNewVehicle.
  ///
  /// In en, this message translates to:
  /// **'Add New Vehicle'**
  String get addNewVehicle;

  /// No description provided for @selectAyear.
  ///
  /// In en, this message translates to:
  /// **'Select a year'**
  String get selectAyear;

  /// No description provided for @pleaseChooseAcolour.
  ///
  /// In en, this message translates to:
  /// **'Please choose a colour'**
  String get pleaseChooseAcolour;

  /// No description provided for @pleaseSelectACountry.
  ///
  /// In en, this message translates to:
  /// **'Please select a country'**
  String get pleaseSelectACountry;

  /// No description provided for @pleaseSelectAVehicleType.
  ///
  /// In en, this message translates to:
  /// **'Please select a vehicle type'**
  String get pleaseSelectAVehicleType;

  /// No description provided for @pleaseSelectAOwnerType.
  ///
  /// In en, this message translates to:
  /// **'Please Select A Owner Type'**
  String get pleaseSelectAOwnerType;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @yourGarageisEmpty.
  ///
  /// In en, this message translates to:
  /// **'Your Garage Is Empty'**
  String get yourGarageisEmpty;

  /// No description provided for @noVehicleSubTitile.
  ///
  /// In en, this message translates to:
  /// **'Add your vehicles to book parking effortlessly.'**
  String get noVehicleSubTitile;

  /// No description provided for @customHours.
  ///
  /// In en, this message translates to:
  /// **'Custom Hours'**
  String get customHours;

  /// No description provided for @hr.
  ///
  /// In en, this message translates to:
  /// **'/hr'**
  String get hr;

  /// No description provided for @kmAway.
  ///
  /// In en, this message translates to:
  /// **'Km Away'**
  String get kmAway;

  /// No description provided for @mAway.
  ///
  /// In en, this message translates to:
  /// **'m Away'**
  String get mAway;

  /// No description provided for @accountDeleted.
  ///
  /// In en, this message translates to:
  /// **'Account Deleted'**
  String get accountDeleted;

  /// No description provided for @accountDeletedSub.
  ///
  /// In en, this message translates to:
  /// **'Your account has been deleted.\nWe\'re sorry to see you go.'**
  String get accountDeletedSub;

  /// No description provided for @scanPlate.
  ///
  /// In en, this message translates to:
  /// **'Scan plate'**
  String get scanPlate;

  /// No description provided for @uploadYourVehicleImage.
  ///
  /// In en, this message translates to:
  /// **'Upload your vehicle image'**
  String get uploadYourVehicleImage;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @violation.
  ///
  /// In en, this message translates to:
  /// **'Violation'**
  String get violation;

  /// No description provided for @primary.
  ///
  /// In en, this message translates to:
  /// **'Primary'**
  String get primary;

  /// No description provided for @vehicleColor.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Color'**
  String get vehicleColor;

  /// No description provided for @searchColour.
  ///
  /// In en, this message translates to:
  /// **'Search Colour'**
  String get searchColour;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @ownerType.
  ///
  /// In en, this message translates to:
  /// **'Owner Type'**
  String get ownerType;

  /// No description provided for @vehicleType.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type'**
  String get vehicleType;

  /// No description provided for @privacy_policy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacy_policy;

  /// No description provided for @notification_settings.
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notification_settings;

  /// No description provided for @receive_updates_and_alerts_via_email.
  ///
  /// In en, this message translates to:
  /// **'Receive updates and alerts via email.'**
  String get receive_updates_and_alerts_via_email;

  /// No description provided for @stay_informed_with_app_notifications.
  ///
  /// In en, this message translates to:
  /// **'Stay informed with app notifications'**
  String get stay_informed_with_app_notifications;

  /// No description provided for @contact_us.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contact_us;

  /// No description provided for @how_can_we_assist.
  ///
  /// In en, this message translates to:
  /// **'How Can We Assist?'**
  String get how_can_we_assist;

  /// No description provided for @our_team_is_here_to_help.
  ///
  /// In en, this message translates to:
  /// **'Our team is here to help.'**
  String get our_team_is_here_to_help;

  /// No description provided for @stay_connected_with_us.
  ///
  /// In en, this message translates to:
  /// **'Stay Connected with Us'**
  String get stay_connected_with_us;

  /// No description provided for @follow_us_on_our_social_media.
  ///
  /// In en, this message translates to:
  /// **'Follow us on our social media channels for updates,\nnews, and support.'**
  String get follow_us_on_our_social_media;

  /// No description provided for @seek_help.
  ///
  /// In en, this message translates to:
  /// **'Seek Help'**
  String get seek_help;

  /// No description provided for @let_us_know_what_youre_looking_for.
  ///
  /// In en, this message translates to:
  /// **'Let us know what you\'re looking for or any issue you\'re\nfacing. We\'ll get back to you with the best solution.'**
  String get let_us_know_what_youre_looking_for;

  /// No description provided for @share_your_problem.
  ///
  /// In en, this message translates to:
  /// **'Share your problem'**
  String get share_your_problem;

  /// No description provided for @please_share_your_problem.
  ///
  /// In en, this message translates to:
  /// **'Please share your problem'**
  String get please_share_your_problem;

  /// No description provided for @please_share_more_details.
  ///
  /// In en, this message translates to:
  /// **'Please share more details'**
  String get please_share_more_details;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @find_the_best_parking.
  ///
  /// In en, this message translates to:
  /// **'Find The Best Parking\nSpot Near You'**
  String get find_the_best_parking;

  /// No description provided for @seamless_parking.
  ///
  /// In en, this message translates to:
  /// **'Seamless Parking, Anytime\nAnywhere. Let’s get you parked!'**
  String get seamless_parking;

  /// No description provided for @lets_get_started.
  ///
  /// In en, this message translates to:
  /// **'Let’s Get Started'**
  String get lets_get_started;

  /// No description provided for @otpValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter a otp first'**
  String get otpValidation;

  /// No description provided for @enableLocationServices.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Services'**
  String get enableLocationServices;

  /// No description provided for @locationPermissionSub.
  ///
  /// In en, this message translates to:
  /// **'Location services are disabled. Please enable them to proceed.'**
  String get locationPermissionSub;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @enable.
  ///
  /// In en, this message translates to:
  /// **'Enable'**
  String get enable;

  /// No description provided for @feedbackValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter feedback'**
  String get feedbackValidation;

  /// No description provided for @backtoEdit.
  ///
  /// In en, this message translates to:
  /// **'Back to Edit'**
  String get backtoEdit;

  /// No description provided for @confirmSave.
  ///
  /// In en, this message translates to:
  /// **'Confirm & Save'**
  String get confirmSave;

  /// No description provided for @active_parking.
  ///
  /// In en, this message translates to:
  /// **'Active Parking'**
  String get active_parking;

  /// No description provided for @my_parking.
  ///
  /// In en, this message translates to:
  /// **'My Parking'**
  String get my_parking;

  /// No description provided for @find_my_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Find My Vehicle'**
  String get find_my_vehicle;

  /// No description provided for @extend.
  ///
  /// In en, this message translates to:
  /// **'Extend'**
  String get extend;

  /// No description provided for @rebook.
  ///
  /// In en, this message translates to:
  /// **'Rebook'**
  String get rebook;

  /// No description provided for @ongoing.
  ///
  /// In en, this message translates to:
  /// **'Ongoing'**
  String get ongoing;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @vat_amount.
  ///
  /// In en, this message translates to:
  /// **'VAT Amount'**
  String get vat_amount;

  /// No description provided for @total_amount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get total_amount;

  /// No description provided for @payment_method.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get payment_method;

  /// No description provided for @update_vehicle_location.
  ///
  /// In en, this message translates to:
  /// **'Update Vehicle Location'**
  String get update_vehicle_location;

  /// No description provided for @know_the_current_location_of_your_car.
  ///
  /// In en, this message translates to:
  /// **'Know the current location of your car.'**
  String get know_the_current_location_of_your_car;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @time_remaining.
  ///
  /// In en, this message translates to:
  /// **'Time remaining'**
  String get time_remaining;

  /// No description provided for @parking_details.
  ///
  /// In en, this message translates to:
  /// **'Parking Details'**
  String get parking_details;

  /// No description provided for @extend_parking_time.
  ///
  /// In en, this message translates to:
  /// **'Extend Parking Time'**
  String get extend_parking_time;

  /// No description provided for @allServices.
  ///
  /// In en, this message translates to:
  /// **'All Services'**
  String get allServices;

  /// No description provided for @parkingSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'Parking Subscriptions'**
  String get parkingSubscriptions;

  /// No description provided for @parkingSubscriptionsSub.
  ///
  /// In en, this message translates to:
  /// **'All your parking subscriptions and details'**
  String get parkingSubscriptionsSub;

  /// No description provided for @subscriptionExpiresIn.
  ///
  /// In en, this message translates to:
  /// **'Subscription expires in'**
  String get subscriptionExpiresIn;

  /// No description provided for @twoDays.
  ///
  /// In en, this message translates to:
  /// **'2 days'**
  String get twoDays;

  /// No description provided for @renewNow.
  ///
  /// In en, this message translates to:
  /// **'Renew Now'**
  String get renewNow;

  /// No description provided for @violations.
  ///
  /// In en, this message translates to:
  /// **'Violations'**
  String get violations;

  /// No description provided for @violationSub.
  ///
  /// In en, this message translates to:
  /// **'Violation tickets list'**
  String get violationSub;

  /// No description provided for @violationTickets.
  ///
  /// In en, this message translates to:
  /// **'Violation tickets'**
  String get violationTickets;

  /// No description provided for @threePending.
  ///
  /// In en, this message translates to:
  /// **'3 Pending'**
  String get threePending;

  /// No description provided for @settleAll.
  ///
  /// In en, this message translates to:
  /// **'Settle All'**
  String get settleAll;

  /// No description provided for @valetBooking.
  ///
  /// In en, this message translates to:
  /// **'Valet Booking'**
  String get valetBooking;

  /// No description provided for @enjoyAhasslefreeParkingExperience.
  ///
  /// In en, this message translates to:
  /// **'Enjoy a hassle-free parking experience'**
  String get enjoyAhasslefreeParkingExperience;

  /// No description provided for @emptyViolationsReported.
  ///
  /// In en, this message translates to:
  /// **'No Violations Reported'**
  String get emptyViolationsReported;

  /// No description provided for @emptyViolationsReportedSub.
  ///
  /// In en, this message translates to:
  /// **'No violations have been reported at this time.'**
  String get emptyViolationsReportedSub;

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// No description provided for @sortBy.
  ///
  /// In en, this message translates to:
  /// **'Sort By'**
  String get sortBy;

  /// No description provided for @violationDetails.
  ///
  /// In en, this message translates to:
  /// **'Violation Details'**
  String get violationDetails;

  /// No description provided for @violationDescription.
  ///
  /// In en, this message translates to:
  /// **'Violation Description'**
  String get violationDescription;

  /// No description provided for @violationImages.
  ///
  /// In en, this message translates to:
  /// **'Violation Images'**
  String get violationImages;

  /// No description provided for @findMyVehicle.
  ///
  /// In en, this message translates to:
  /// **'Find My Vehicle'**
  String get findMyVehicle;

  /// No description provided for @findMyVehicleSub.
  ///
  /// In en, this message translates to:
  /// **'Know the current location of your car'**
  String get findMyVehicleSub;

  /// No description provided for @settleViolation.
  ///
  /// In en, this message translates to:
  /// **'Settle Violation'**
  String get settleViolation;

  /// No description provided for @nolocationfound.
  ///
  /// In en, this message translates to:
  /// **'No location found'**
  String get nolocationfound;

  /// No description provided for @nocolorfound.
  ///
  /// In en, this message translates to:
  /// **'No color found'**
  String get nocolorfound;

  /// No description provided for @scannedNumberPlate.
  ///
  /// In en, this message translates to:
  /// **'Scanned Plate Number'**
  String get scannedNumberPlate;

  /// No description provided for @scanNumberPlate.
  ///
  /// In en, this message translates to:
  /// **'Scan Number Plate'**
  String get scanNumberPlate;

  /// No description provided for @plateNumberDiffer.
  ///
  /// In en, this message translates to:
  /// **'License plate numbers are different'**
  String get plateNumberDiffer;

  /// No description provided for @selectPlateValidation.
  ///
  /// In en, this message translates to:
  /// **'Please select a plate type'**
  String get selectPlateValidation;

  /// No description provided for @normalVehicleNumberValidation.
  ///
  /// In en, this message translates to:
  /// **'Enter vehicle number'**
  String get normalVehicleNumberValidation;

  /// No description provided for @noFavorites.
  ///
  /// In en, this message translates to:
  /// **'No Favorites'**
  String get noFavorites;

  /// No description provided for @noFavoritesSub.
  ///
  /// In en, this message translates to:
  /// **'Save your preferred parking locations for quick access next time.'**
  String get noFavoritesSub;

  /// No description provided for @qrScanner.
  ///
  /// In en, this message translates to:
  /// **'QR Scanner'**
  String get qrScanner;

  /// No description provided for @bookNewValet.
  ///
  /// In en, this message translates to:
  /// **'Book New Valet'**
  String get bookNewValet;

  /// No description provided for @chooseSpot.
  ///
  /// In en, this message translates to:
  /// **'Choose Spot'**
  String get chooseSpot;

  /// No description provided for @searchSpot.
  ///
  /// In en, this message translates to:
  /// **'Search Spot'**
  String get searchSpot;

  /// No description provided for @nospotfound.
  ///
  /// In en, this message translates to:
  /// **'No spot found'**
  String get nospotfound;

  /// No description provided for @valet_service.
  ///
  /// In en, this message translates to:
  /// **'Valet Service Charges'**
  String get valet_service;

  /// No description provided for @valet_serviceSub.
  ///
  /// In en, this message translates to:
  /// **'Enjoy hassle-free parking with our reliable valet services. Secure, convenient, and ready when you need it'**
  String get valet_serviceSub;

  /// No description provided for @selectTime.
  ///
  /// In en, this message translates to:
  /// **'Select Time'**
  String get selectTime;

  /// No description provided for @confirmBooking.
  ///
  /// In en, this message translates to:
  /// **'Confirm Booking'**
  String get confirmBooking;

  /// No description provided for @valet.
  ///
  /// In en, this message translates to:
  /// **'Valet'**
  String get valet;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @noValetBookings.
  ///
  /// In en, this message translates to:
  /// **'No Valet Bookings'**
  String get noValetBookings;

  /// No description provided for @noValetSub.
  ///
  /// In en, this message translates to:
  /// **'Book a valet service to enjoy hassle-free parking.'**
  String get noValetSub;

  /// No description provided for @valetBookingSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Valet Booking Successful'**
  String get valetBookingSuccessful;

  /// No description provided for @valetbookingSub.
  ///
  /// In en, this message translates to:
  /// **'Your valet service has been reserved. Thank you!'**
  String get valetbookingSub;

  /// No description provided for @valetPaymentSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Valet Payment Successful'**
  String get valetPaymentSuccessful;

  /// No description provided for @valetPaymentSuccessfulSub.
  ///
  /// In en, this message translates to:
  /// **'Your Valet payment is successful'**
  String get valetPaymentSuccessfulSub;

  /// No description provided for @requestVehicle.
  ///
  /// In en, this message translates to:
  /// **'Request Vehicle'**
  String get requestVehicle;

  /// No description provided for @approveVehicleAccess.
  ///
  /// In en, this message translates to:
  /// **'Approve Vehicle Access'**
  String get approveVehicleAccess;

  /// No description provided for @yourEmptyRequest.
  ///
  /// In en, this message translates to:
  /// **'You have no request'**
  String get yourEmptyRequest;

  /// No description provided for @packages.
  ///
  /// In en, this message translates to:
  /// **'Packages'**
  String get packages;

  /// No description provided for @chooseThePackageYouLike.
  ///
  /// In en, this message translates to:
  /// **'Choose the package you like'**
  String get chooseThePackageYouLike;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get change;

  /// No description provided for @license_plate_numbers_are_different.
  ///
  /// In en, this message translates to:
  /// **'License plate numbers are different'**
  String get license_plate_numbers_are_different;

  /// No description provided for @please_enter_license_plate_number.
  ///
  /// In en, this message translates to:
  /// **'Please enter license plate number'**
  String get please_enter_license_plate_number;

  /// No description provided for @confirm_location_update.
  ///
  /// In en, this message translates to:
  /// **'Confirm Location Update'**
  String get confirm_location_update;

  /// No description provided for @confirm_location_update_message.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to update the location of this vehicle? This change will reflect immediately.'**
  String get confirm_location_update_message;

  /// No description provided for @noAvailableSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'No Available Subscriptions'**
  String get noAvailableSubscriptions;

  /// No description provided for @noAvailableResidentialSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'No Available Residential Subscriptions'**
  String get noAvailableResidentialSubscriptions;

  /// No description provided for @enterNationalAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter national address'**
  String get enterNationalAddress;

  /// No description provided for @nationalAddress.
  ///
  /// In en, this message translates to:
  /// **'National Address*'**
  String get nationalAddress;

  /// No description provided for @document.
  ///
  /// In en, this message translates to:
  /// **'Document'**
  String get document;

  /// No description provided for @uploadDocument.
  ///
  /// In en, this message translates to:
  /// **'Upload Document'**
  String get uploadDocument;

  /// No description provided for @yourParkingSubscriptionActiveNow.
  ///
  /// In en, this message translates to:
  /// **'Your Parking subscription is active now'**
  String get yourParkingSubscriptionActiveNow;

  /// No description provided for @yourParkingSubscriptionActiveSub.
  ///
  /// In en, this message translates to:
  /// **'Enjoy seamless parking with exclusive\nfeatures and benefits'**
  String get yourParkingSubscriptionActiveSub;

  /// No description provided for @addNewSubscription.
  ///
  /// In en, this message translates to:
  /// **'Add New Subscription'**
  String get addNewSubscription;

  /// No description provided for @activeResidentialSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'Active Residential Subscriptions'**
  String get activeResidentialSubscriptions;

  /// No description provided for @activeSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'Active Subscriptions'**
  String get activeSubscriptions;

  /// No description provided for @expireOn.
  ///
  /// In en, this message translates to:
  /// **'Expires on'**
  String get expireOn;

  /// No description provided for @totalAmountPaid.
  ///
  /// In en, this message translates to:
  /// **'Total Amount Paid'**
  String get totalAmountPaid;

  /// No description provided for @payNowActivate.
  ///
  /// In en, this message translates to:
  /// **'Pay Now & Activate'**
  String get payNowActivate;

  /// No description provided for @renewSubscription.
  ///
  /// In en, this message translates to:
  /// **'Renew Subscription'**
  String get renewSubscription;

  /// No description provided for @expiredSubscriptions.
  ///
  /// In en, this message translates to:
  /// **'Expired Subscriptions'**
  String get expiredSubscriptions;

  /// No description provided for @expired.
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// No description provided for @subscriptionDetails.
  ///
  /// In en, this message translates to:
  /// **'Subscription Details'**
  String get subscriptionDetails;

  /// No description provided for @startedDate.
  ///
  /// In en, this message translates to:
  /// **'Started Date'**
  String get startedDate;

  /// No description provided for @expiryDate.
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get expiryDate;

  /// No description provided for @vehicleDetails.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Details'**
  String get vehicleDetails;

  /// No description provided for @paymentDetails.
  ///
  /// In en, this message translates to:
  /// **'Payment Details'**
  String get paymentDetails;

  /// No description provided for @no_transactions_yet.
  ///
  /// In en, this message translates to:
  /// **'No transactions yet!'**
  String get no_transactions_yet;

  /// No description provided for @no_transactions_yet_sub.
  ///
  /// In en, this message translates to:
  /// **'Your transactions will appear here\nonce you start using your wallet.'**
  String get no_transactions_yet_sub;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @changeVehicle.
  ///
  /// In en, this message translates to:
  /// **'Change Vehicle'**
  String get changeVehicle;

  /// No description provided for @updateYourVehicle.
  ///
  /// In en, this message translates to:
  /// **'Update Your Vehicle'**
  String get updateYourVehicle;

  /// No description provided for @updateVehicleTitle.
  ///
  /// In en, this message translates to:
  /// **'Your payment has been successfully processed!'**
  String get updateVehicleTitle;

  /// No description provided for @updateVehicleSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Your vehicle changed successfully.Thank you!'**
  String get updateVehicleSubTitle;

  /// No description provided for @residentialSubscriptionPayment.
  ///
  /// In en, this message translates to:
  /// **'Residential Subscription Payment'**
  String get residentialSubscriptionPayment;

  /// No description provided for @residentialSubscriptionpaymentTitle.
  ///
  /// In en, this message translates to:
  /// **'Residential Subscription payment Successful'**
  String get residentialSubscriptionpaymentTitle;

  /// No description provided for @residentialSubscriptionpaymentSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Your payment for residential subscription is successfully. enjoy parking at ease.'**
  String get residentialSubscriptionpaymentSubTitle;

  /// No description provided for @subscription_renewal_message.
  ///
  /// In en, this message translates to:
  /// **'Your subscriptions renewed successfully.\nThank you !'**
  String get subscription_renewal_message;

  /// No description provided for @no_notifications_yet.
  ///
  /// In en, this message translates to:
  /// **'No notifications yet !'**
  String get no_notifications_yet;

  /// No description provided for @no_data_found.
  ///
  /// In en, this message translates to:
  /// **'No data found'**
  String get no_data_found;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @expires_in.
  ///
  /// In en, this message translates to:
  /// **'Expires in'**
  String get expires_in;

  /// No description provided for @disabled.
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'pending'**
  String get pending;

  /// No description provided for @violation_tickets.
  ///
  /// In en, this message translates to:
  /// **'Violation tickets'**
  String get violation_tickets;

  /// No description provided for @violation_ticket.
  ///
  /// In en, this message translates to:
  /// **'Violation ticket'**
  String get violation_ticket;

  /// No description provided for @pleaseAddVehiclefirst.
  ///
  /// In en, this message translates to:
  /// **'Please add vehicle first'**
  String get pleaseAddVehiclefirst;

  /// No description provided for @requestSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Request successful'**
  String get requestSuccessful;

  /// No description provided for @requestSuccessfulMsg.
  ///
  /// In en, this message translates to:
  /// **'Vehicle request successfully sent'**
  String get requestSuccessfulMsg;

  /// No description provided for @payment_successful_violation_message.
  ///
  /// In en, this message translates to:
  /// **'All your violation tickets has been cleared. Thank you for resolving the issue promptly.'**
  String get payment_successful_violation_message;

  /// No description provided for @faq.
  ///
  /// In en, this message translates to:
  /// **'FAQ’s'**
  String get faq;

  /// No description provided for @faqTitile.
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get faqTitile;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @pressBackAgainToClose.
  ///
  /// In en, this message translates to:
  /// **'Press back again to close'**
  String get pressBackAgainToClose;

  /// No description provided for @universal.
  ///
  /// In en, this message translates to:
  /// **'Universal'**
  String get universal;

  /// No description provided for @continue_to_payment.
  ///
  /// In en, this message translates to:
  /// **'Continue to Payment'**
  String get continue_to_payment;

  /// No description provided for @expires_on.
  ///
  /// In en, this message translates to:
  /// **'Expires on'**
  String get expires_on;

  /// No description provided for @total_amount_caps.
  ///
  /// In en, this message translates to:
  /// **'TOTAL AMOUNT'**
  String get total_amount_caps;

  /// No description provided for @start_date.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get start_date;

  /// No description provided for @alllicensefieldarerequired.
  ///
  /// In en, this message translates to:
  /// **'All license fields are required'**
  String get alllicensefieldarerequired;

  /// No description provided for @empty_inbox.
  ///
  /// In en, this message translates to:
  /// **'Empty Inbox'**
  String get empty_inbox;

  /// No description provided for @no_notification_message.
  ///
  /// In en, this message translates to:
  /// **'No notification right now, Please\ncheck back again later'**
  String get no_notification_message;

  /// No description provided for @yourParkingSubscriptionRequeted.
  ///
  /// In en, this message translates to:
  /// **'Residential Subscription\nRequest Submitted.'**
  String get yourParkingSubscriptionRequeted;

  /// No description provided for @yourParkingSubscriptionRequetedSub.
  ///
  /// In en, this message translates to:
  /// **'Your request for residential subscription\nhave submitted successfully. we will sent an\napproval notification and with the updated price'**
  String get yourParkingSubscriptionRequetedSub;

  /// No description provided for @ticketDetails.
  ///
  /// In en, this message translates to:
  /// **'Ticket Details'**
  String get ticketDetails;

  /// No description provided for @requestAgain.
  ///
  /// In en, this message translates to:
  /// **'Request Again'**
  String get requestAgain;

  /// No description provided for @residentialSubscriptionRequestRejected.
  ///
  /// In en, this message translates to:
  /// **'Residential Subscription Request Rejected.'**
  String get residentialSubscriptionRequestRejected;

  /// No description provided for @residentialSubscriptionRequestRejectedSub.
  ///
  /// In en, this message translates to:
  /// **'Your Residential request has been rejected as the attached document is not vaild.'**
  String get residentialSubscriptionRequestRejectedSub;

  /// No description provided for @locationNotFound.
  ///
  /// In en, this message translates to:
  /// **'Location Not Found'**
  String get locationNotFound;

  /// No description provided for @locationNotFoundSub.
  ///
  /// In en, this message translates to:
  /// **'Sorry, we couldn’t find what you’re looking for\nPlease try searching a different location.'**
  String get locationNotFoundSub;

  /// No description provided for @locationNotFoundQr.
  ///
  /// In en, this message translates to:
  /// **'Please check the QR code and try again.'**
  String get locationNotFoundQr;

  /// No description provided for @clearAll.
  ///
  /// In en, this message translates to:
  /// **'Clear all'**
  String get clearAll;

  /// No description provided for @recentSearch.
  ///
  /// In en, this message translates to:
  /// **'Recent Searches'**
  String get recentSearch;

  /// No description provided for @showingResultsfor.
  ///
  /// In en, this message translates to:
  /// **'Showing Results for'**
  String get showingResultsfor;

  /// No description provided for @violation_id.
  ///
  /// In en, this message translates to:
  /// **'Violation ID'**
  String get violation_id;

  /// No description provided for @valet_id.
  ///
  /// In en, this message translates to:
  /// **'Valet ID'**
  String get valet_id;

  /// No description provided for @parking_id.
  ///
  /// In en, this message translates to:
  /// **'Parking ID'**
  String get parking_id;

  /// No description provided for @enter_amount.
  ///
  /// In en, this message translates to:
  /// **'Please enter or choose the amount to recharge'**
  String get enter_amount;

  /// No description provided for @add_upto_five_thousand.
  ///
  /// In en, this message translates to:
  /// **'You can add upto 5000 SAR'**
  String get add_upto_five_thousand;

  /// No description provided for @subscription_type.
  ///
  /// In en, this message translates to:
  /// **'Subscription Type'**
  String get subscription_type;

  /// No description provided for @minute.
  ///
  /// In en, this message translates to:
  /// **'minute'**
  String get minute;

  /// No description provided for @minutes.
  ///
  /// In en, this message translates to:
  /// **'minutes'**
  String get minutes;

  /// No description provided for @subscription_has_yet_message.
  ///
  /// In en, this message translates to:
  /// **'Subscription has expired. renew it to now to enjoy the benefits'**
  String get subscription_has_yet_message;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'status'**
  String get status;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @okay.
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get okay;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @purchasedBy.
  ///
  /// In en, this message translates to:
  /// **'Purchased by'**
  String get purchasedBy;

  /// No description provided for @oops.
  ///
  /// In en, this message translates to:
  /// **'Oops'**
  String get oops;

  /// No description provided for @getDirection.
  ///
  /// In en, this message translates to:
  /// **'Get Direction'**
  String get getDirection;

  /// No description provided for @headsUp.
  ///
  /// In en, this message translates to:
  /// **'Heads Up'**
  String get headsUp;

  /// No description provided for @sar.
  ///
  /// In en, this message translates to:
  /// **'SAR'**
  String get sar;

  /// No description provided for @hr_short.
  ///
  /// In en, this message translates to:
  /// **'hr'**
  String get hr_short;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'hours'**
  String get hours;

  /// No description provided for @hours_caps.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours_caps;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'hour'**
  String get hour;

  /// No description provided for @hour_caps.
  ///
  /// In en, this message translates to:
  /// **'Hour'**
  String get hour_caps;

  /// No description provided for @min.
  ///
  /// In en, this message translates to:
  /// **'min'**
  String get min;

  /// No description provided for @mins.
  ///
  /// In en, this message translates to:
  /// **'mins'**
  String get mins;

  /// No description provided for @min_caps.
  ///
  /// In en, this message translates to:
  /// **'Min'**
  String get min_caps;

  /// No description provided for @mins_caps.
  ///
  /// In en, this message translates to:
  /// **'Mins'**
  String get mins_caps;

  /// No description provided for @minute_caps.
  ///
  /// In en, this message translates to:
  /// **'Minute'**
  String get minute_caps;

  /// No description provided for @minutes_caps.
  ///
  /// In en, this message translates to:
  /// **'Minutes'**
  String get minutes_caps;

  /// No description provided for @away.
  ///
  /// In en, this message translates to:
  /// **'away'**
  String get away;

  /// No description provided for @invoice_id.
  ///
  /// In en, this message translates to:
  /// **'Invoice ID'**
  String get invoice_id;

  /// No description provided for @bill_to.
  ///
  /// In en, this message translates to:
  /// **'Bill To'**
  String get bill_to;

  /// No description provided for @service_type.
  ///
  /// In en, this message translates to:
  /// **'Service Type'**
  String get service_type;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @on_street.
  ///
  /// In en, this message translates to:
  /// **'On-Street'**
  String get on_street;

  /// No description provided for @off_street.
  ///
  /// In en, this message translates to:
  /// **'Off-Street'**
  String get off_street;

  /// No description provided for @didnt_received_the_otp.
  ///
  /// In en, this message translates to:
  /// **'Didn’t received the OTP?'**
  String get didnt_received_the_otp;

  /// No description provided for @resend_code.
  ///
  /// In en, this message translates to:
  /// **'Resend code'**
  String get resend_code;

  /// No description provided for @free_parking_hours.
  ///
  /// In en, this message translates to:
  /// **'Free parking hours'**
  String get free_parking_hours;

  /// No description provided for @no_country_found.
  ///
  /// In en, this message translates to:
  /// **'No country found'**
  String get no_country_found;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @no_subscriptions_available.
  ///
  /// In en, this message translates to:
  /// **'No subscriptions available'**
  String get no_subscriptions_available;

  /// No description provided for @favorites.
  ///
  /// In en, this message translates to:
  /// **'Favorites'**
  String get favorites;

  /// No description provided for @no_active_parking.
  ///
  /// In en, this message translates to:
  /// **'No active parking'**
  String get no_active_parking;

  /// No description provided for @parking_location_message.
  ///
  /// In en, this message translates to:
  /// **'Start by selecting a parking location to book your\nspot easily.'**
  String get parking_location_message;

  /// No description provided for @book_parking.
  ///
  /// In en, this message translates to:
  /// **'Book Parking'**
  String get book_parking;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @please_share_your_feedback.
  ///
  /// In en, this message translates to:
  /// **'Please share your feedback'**
  String get please_share_your_feedback;

  /// No description provided for @something_went_wrong.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong!'**
  String get something_went_wrong;

  /// No description provided for @fined_amount.
  ///
  /// In en, this message translates to:
  /// **'FINED AMOUNT'**
  String get fined_amount;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @alert.
  ///
  /// In en, this message translates to:
  /// **'Alert'**
  String get alert;

  /// No description provided for @noInternet.
  ///
  /// In en, this message translates to:
  /// **'No internet connection. Please check your connection.'**
  String get noInternet;

  /// No description provided for @name_required.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get name_required;

  /// No description provided for @terms_and_conditions_required.
  ///
  /// In en, this message translates to:
  /// **'Please agree to the terms and conditions'**
  String get terms_and_conditions_required;

  /// No description provided for @please_enter_valid_otp.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid OTP.'**
  String get please_enter_valid_otp;

  /// No description provided for @otp_success.
  ///
  /// In en, this message translates to:
  /// **'OTP verified successfully!'**
  String get otp_success;

  /// No description provided for @invalid_otp.
  ///
  /// In en, this message translates to:
  /// **'Invalid OTP. Please try again.'**
  String get invalid_otp;

  /// No description provided for @error_occured.
  ///
  /// In en, this message translates to:
  /// **'An error occurred. Please try again.'**
  String get error_occured;

  /// No description provided for @inbound.
  ///
  /// In en, this message translates to:
  /// **'Inbound'**
  String get inbound;

  /// No description provided for @outbound.
  ///
  /// In en, this message translates to:
  /// **'Outbound'**
  String get outbound;

  /// No description provided for @please_select_vehicle_country.
  ///
  /// In en, this message translates to:
  /// **'Please select vehicle country'**
  String get please_select_vehicle_country;

  /// No description provided for @vehicle_name_required.
  ///
  /// In en, this message translates to:
  /// **'Please enter your vehicle name'**
  String get vehicle_name_required;

  /// No description provided for @vehicle_type_required.
  ///
  /// In en, this message translates to:
  /// **'Please select vehicle type'**
  String get vehicle_type_required;

  /// No description provided for @ownership_required.
  ///
  /// In en, this message translates to:
  /// **'Please select ownership type'**
  String get ownership_required;

  /// No description provided for @paymentfailed.
  ///
  /// In en, this message translates to:
  /// **'Payment failed'**
  String get paymentfailed;

  /// No description provided for @enjoySeamlessParkingWithExclusiveFeatureAandBenefits.
  ///
  /// In en, this message translates to:
  /// **'Enjoy seamless parking with exclusive features and benefits'**
  String get enjoySeamlessParkingWithExclusiveFeatureAandBenefits;

  /// No description provided for @pleaseChooseSpot.
  ///
  /// In en, this message translates to:
  /// **'Please choose a spot'**
  String get pleaseChooseSpot;

  /// No description provided for @pleaseSelectTime.
  ///
  /// In en, this message translates to:
  /// **'Please select time'**
  String get pleaseSelectTime;

  /// No description provided for @pleaseSelectFutureTime.
  ///
  /// In en, this message translates to:
  /// **'Please select a future time'**
  String get pleaseSelectFutureTime;

  /// No description provided for @plate_number_error.
  ///
  /// In en, this message translates to:
  /// **'Plate Number Api Error'**
  String get plate_number_error;

  /// No description provided for @changeVehicleSub.
  ///
  /// In en, this message translates to:
  /// **'Would you like to change your vehicle for just '**
  String get changeVehicleSub;

  /// No description provided for @changeVehicleSubTwo.
  ///
  /// In en, this message translates to:
  /// **'. Vehicle can only be changed once'**
  String get changeVehicleSubTwo;

  /// No description provided for @payment_failed.
  ///
  /// In en, this message translates to:
  /// **'Payment Failed'**
  String get payment_failed;

  /// No description provided for @payment_failed_message.
  ///
  /// In en, this message translates to:
  /// **'Oops! Something went wrong with your payment. Please check your payment details and try again. If the issue persists, contact support for assistance.'**
  String get payment_failed_message;

  /// No description provided for @add_new_card.
  ///
  /// In en, this message translates to:
  /// **'Add New Card'**
  String get add_new_card;

  /// No description provided for @enter_card_details.
  ///
  /// In en, this message translates to:
  /// **'Enter Card Details'**
  String get enter_card_details;

  /// No description provided for @card_holder_name_star.
  ///
  /// In en, this message translates to:
  /// **'Card Holder Name*'**
  String get card_holder_name_star;

  /// No description provided for @card_number_star.
  ///
  /// In en, this message translates to:
  /// **'Card Number*'**
  String get card_number_star;

  /// No description provided for @expiry_date.
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get expiry_date;

  /// No description provided for @cvv.
  ///
  /// In en, this message translates to:
  /// **'CVV'**
  String get cvv;

  /// No description provided for @save_card_text.
  ///
  /// In en, this message translates to:
  /// **'Save this card for future payment'**
  String get save_card_text;

  /// No description provided for @enter_card_holder_name.
  ///
  /// In en, this message translates to:
  /// **'Enter Card Holder Name'**
  String get enter_card_holder_name;

  /// No description provided for @make_payment.
  ///
  /// In en, this message translates to:
  /// **'Make Payment'**
  String get make_payment;

  /// No description provided for @please_enter_card_holder_name.
  ///
  /// In en, this message translates to:
  /// **'Please enter card holder name'**
  String get please_enter_card_holder_name;

  /// No description provided for @please_enter_card_number.
  ///
  /// In en, this message translates to:
  /// **'Please enter card number'**
  String get please_enter_card_number;

  /// No description provided for @please_enter_expiry_date.
  ///
  /// In en, this message translates to:
  /// **'Please enter expiry date'**
  String get please_enter_expiry_date;

  /// No description provided for @please_enter_cvv.
  ///
  /// In en, this message translates to:
  /// **'Please enter CVV'**
  String get please_enter_cvv;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
