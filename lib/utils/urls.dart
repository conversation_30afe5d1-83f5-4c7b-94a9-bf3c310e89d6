import 'dart:developer';

import 'package:albalad_parking_app/helper/shared_preference_helper.dart';
import 'package:albalad_parking_app/models/logged_in_user.dart';
import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';

class Api {
  static Future<Map<String, String>> getAuthorizationHeader() async {
    debugPrint("${Token.accessToken}");

    return {
      'Authorization': 'Bearer ${Token.accessToken}',
      "Accept-Language": await Lang.getLanguage(),
      "time-zone": await _getTimeZone(),
    };
  }

  static Future<Map<String, String>> headers() async {
    return {
      'time-zone': await _getTimeZone(),
      'X-Client-Type': 'mobile',
      'Accept-Language': await Lang.getLanguage(),
    };
  }
}

class Lang {
  static Future<String> getLanguage() async {
    String? userLocale = SharedPreferenceHelper.instance.getData('locale');

    return userLocale ?? "en";
  }

  static Future<String> getLanguageForPayment() async {
    String? userLocale = SharedPreferenceHelper.instance.getData('locale');

    if (userLocale == "ar") {
      return "ar_SA";
    }
    return "en_US";
  }
}

Future<String> _getTimeZone() async {
  log("time zone ==== ${await FlutterTimezone.getLocalTimezone()}");
  return await FlutterTimezone.getLocalTimezone();
}

enum AppEnvironment {
  development,
  staging,
  microservice
  // dev_2,
}

// -------------------- URLS -------------------------
class ApiConstants {
  // static const _baseUrlDev = 'https://al-balad.e8demo.com'; // old dev
  static const _baseUrlDev = 'https://al-balad-test.e8demo.com';
  // static const _baseUrlStage = 'https://al-balad-stage.e8demo.com'; // stage old
  static const _baseUrlStage = 'https://al-baladstage.e8demo.com'; //new stage
  static const _baseUrlMicroservice =
      'https://balad-customer-api.e8demo.com'; //Microservice
  // static const _baseUrlProd = 'https://al-balad-test.e8demo.com'; // new dev

  static AppEnvironment _currentEnvironment = AppEnvironment.development;

  static String get apiBaseURL {
    switch (_currentEnvironment) {
      case AppEnvironment.development:
        return _baseUrlDev;
      case AppEnvironment.staging:
        return _baseUrlStage;
      case AppEnvironment.microservice:
        return _baseUrlMicroservice;
      // case AppEnvironment.dev_2:
      //   return _baseUrlProd;
    }
  }

  static String get baseURL => '$apiBaseURL/api/customer/v1/';

  // Method to set environment (call this from your splash screen)
  static void setEnvironment(AppEnvironment env) async {
    _currentEnvironment = env;
    await SharedPreferenceHelper.instance.saveData(
      'environment',
      env.toString().split('.').last,
    );
  }

  static AppEnvironment get currentEnvironment => _currentEnvironment;

  static Future<void> initializeEnvironment() async {
    final savedEnv = SharedPreferenceHelper.instance.getData('environment');
    if (savedEnv != null) {
      _currentEnvironment = AppEnvironment.values.firstWhere(
        (e) => e.toString().split('.').last == savedEnv,
        orElse: () => AppEnvironment.development,
      );
    }
  }

  static String get signInURL => "${baseURL}signin/";
  static String get secretKeyURL => "${baseURL}secret-key";
  static String get allLocationURL => "${baseURL}location-list/";
  static String get signUpURL => "${baseURL}signup/";
  static String get termsAndConditionsURL => "${baseURL}terms-and-condition/";
  static String get profileURL => "${baseURL}profile/";
  static String get editprofileURL => "${baseURL}edit-profile/";
  static String get qrcodeScannerURL => "${baseURL}qr-code-scanned-loc/";
  static String get deleteReasonURL => "${baseURL}delete-reason/";
  static String get deleteAccountURL => "${baseURL}delete-account/";
  static String get languageLabelsURL => "${baseURL}language-labels/";
  static String get countriesURL => "${baseURL}countries/";
  static String get vehicleColorsURL => "${baseURL}vehicle-color/";
  static String get vehiclePlateTypeURL => "${baseURL}vehicle-plate-type/";
  static String get ownerTypeURL => "${baseURL}owner-type/";
  static String get vehicleTypeURL => "${baseURL}vehicle-type/";
  static String get addVehicleURL => "${baseURL}vehicle/";
  static String get vehicleListURL => "${baseURL}vehicle-list/";
  static String get vehicleDeleteURL => "${baseURL}vehicle-delete/";
  static String get editVehicleURL => "${baseURL}vehicle-update/";
  static String get primeryVehicleURL => "${baseURL}is-exist-primary/";
  static String get locationAmountURL => "${baseURL}location-amount/";
  static String get parkingBookingURL => "${baseURL}book-parking/";
  static String get privacyPolicyURL => "${baseURL}privacy-policy/";
  static String get contactUsURL => "${baseURL}contact-us/";
  static String get searchLocaionURL => "${baseURL}search-location/";
  static String get searchLocationDetailsURL => "${baseURL}location-details/";
  static String get feedbackURL => "${baseURL}feedback/";
  static String get vehicleSearchURL => "${baseURL}license-plate-search/";
  static String get vehicleRequestListing =>
      "${baseURL}vehicle-request-listing/";
  static String get vehicleRequestApprove => "${baseURL}vehicle-approval/";
//My parking
  static String get activeParkingURL => "${baseURL}active-parking/";
  static String get myParkingURL => "${baseURL}my-parking/";
  static String get parkingDetailsURL => "${baseURL}parking-detail/";
  static String get extendParkingURL => "${baseURL}extend-parking/";
  static String get updateVehicleLocationURL =>
      "${baseURL}update-vehicle-location/";
  // static String get activeValet => "${baseURL}active-valet/";

//Voilations
  static String get getViolationURL => "${baseURL}violation-list/";
  static String get violationWalletPaymentURL =>
      "${baseURL}violation-wallet-payment/";
  static String get findMyVehicleURL => "${baseURL}find-my-vehicle/";
  static String get violationSettlementURL => "${baseURL}violation-settlement/";
  static String get violationDetailURL => "${baseURL}violation-detail/";

// fav
  static String get favouriteListURL => "${baseURL}favourites-list/";
  static String get favouriteURL => "${baseURL}favourites/";

// valet
  static String get valetSpotURL => "${baseURL}valet-spots/";
  static String get getValetMapLoctionsURL => "${baseURL}valet-location-list/";
  static String get valetBookingURL => "${baseURL}valet-booking/";
  static String get valetHistoryURL => "${baseURL}valet-history/";
  static String get valetActiveURL => "${baseURL}active-valet/";
  static String get valetDetailsURL => "${baseURL}valet-ticket-detail/";
  static String get valetSettleURL => "${baseURL}valet-settlement/";
  static String get valetCancelURL => "${baseURL}cancel-valet/";
  static String get requestVehicleURL => "${baseURL}request-vehicle/";
  static String get requestVehicleForValetURL => "${baseURL}vehicle-request/";

// subscriptions
  static String get subscriptionListingURL => "${baseURL}subscription-listing/";
  static String get renewSubscriptionDetailURL =>
      "${baseURL}renew-subscription-detail/";
  static String get subscrptionsListsURL => "${baseURL}subscriptions/";

// My Wallet
  static String get transactionHistoryURL =>
      "${baseURL}wallet-transaction-history/";
  static String get walletDetailURL => "${baseURL}wallet-detail/";
  static String get addWalletBalanceURL => "${baseURL}add-wallet-balance/";
  static String get walletInvoiceURL => "${baseURL}invoice/";
  static String get subscriptionDetailsURL => "${baseURL}subscription-detail/";
  static String get subscriptionCreationURL =>
      "${baseURL}parking-subscription/";
  static String get residentialSubscriptionRequestURL =>
      "${baseURL}residential-subscription-request/";
  static String get activeResidentialSubscriptionListURL =>
      "${baseURL}active-residential-subscription/";
  static String get activeSubscriptionListURL =>
      "${baseURL}active-subscription/";
  static String get expiredSubscriptionListURL =>
      "${baseURL}expired-subscription/";
  static String get parkingSubscriptionDetailsURL =>
      "${baseURL}parking-subscription-detail/";
// corporate subscription
  static String get corporateRequestURL => "${baseURL}corporate-request/";
// Notifications
  static String get notificaitonURL => "${baseURL}notifications/";
  static String get notificaitonReadURL => "${baseURL}read-notification/";
  static String get notificaitonCountURL => "${baseURL}notification-count/";
  static String get renewSubscriptionURL => "${baseURL}renew-subscription/";
  static String get updateVehicleSubScriptionURL =>
      "${baseURL}change-vehicle-payment/";
  static String get residentialSubScriptionPayAndActiveURL =>
      "${baseURL}residential-subscription/";
// All-services
  static String get allServicesURL => "${baseURL}all-services/";
  static String get settleAllViolationURL => "${baseURL}settle-all-violation/";
  static String get bulkViolationSettlementURL =>
      "${baseURL}bulk-violation-settlement/";
  static String get faqURL => "${baseURL}faqs/";
//Notification Settings
  static String get notificationSettingsURL =>
      "${baseURL}notification-settings/";
  static String get notificationSettingsUpdateURL =>
      "${baseURL}notification-settings-update/";
//Social Media Accounts
  static String get socialMediaAccountsURL =>
      "${baseURL}social-media-accounts/";

// Advertisement
  static String get advertisementURL => "${baseURL}ads-listing/";

// ---- payment-status

  static String get paymentStatusURL => "${baseURL}payment-status/";

  static String get signOutURL => "${baseURL}signout/";
  static String get refreshTokenURL => "${baseURL}refresh-token/";
  static String get mobileApplicationDetail => '${baseURL}mobile-application/';

// FCM

  static String get refreshFcmURL => '${baseURL}refresh-fcm-token/';

// Language
  static String get languageSwitchingURL => '${baseURL}language/';

  //Support Ticket
  static String get supportTicketURL => '${baseURL}zoho-ticket/';
  static String get ticketsListURL => '${baseURL}tickets/';
}
