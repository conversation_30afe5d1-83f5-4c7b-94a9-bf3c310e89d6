/// Utility functions for handling HTML content
class HtmlUtils {
  /// Strips HTML tags from a string and returns plain text
  static String stripHtmlTags(String htmlString) {
    if (htmlString.isEmpty) return htmlString;
    
    // Remove HTML tags using regex
    final RegExp htmlTagRegex = RegExp(r'<[^>]*>');
    String plainText = htmlString.replaceAll(htmlTagRegex, '');
    
    // Decode common HTML entities
    plainText = plainText
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#x27;', "'")
        .replaceAll('&#x2F;', '/')
        .replaceAll('&#x5b;', '[')
        .replaceAll('&#x5d;', ']')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('<br>', '\n')
        .replaceAll('<br/>', '\n')
        .replaceAll('<br />', '\n');
    
    // Clean up extra whitespace
    plainText = plainText
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return plainText;
  }
  
  /// Checks if a string contains HTML tags
  static bool containsHtml(String text) {
    final RegExp htmlTagRegex = RegExp(r'<[^>]*>');
    return htmlTagRegex.hasMatch(text);
  }
}
