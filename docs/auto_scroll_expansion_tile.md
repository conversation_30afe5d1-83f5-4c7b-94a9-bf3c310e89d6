# Auto-Scroll ExpansionTile Implementation

## Overview

The auto-scroll functionality has been implemented in the FAQ screen to automatically scroll and show expanded content when a user taps on an ExpansionTile that's positioned at the bottom of the screen. This ensures that users can always see the full expanded content without manual scrolling.

## How It Works

### 1. **ScrollController Integration**
- A `ScrollController` is attached to the ListView containing the FAQ items
- This controller manages the automatic scrolling behavior

### 2. **GlobalKey Positioning**
- Each FAQ item container is assigned a unique `GlobalKey`
- These keys allow precise position calculation of each item on the screen
- Keys are generated using `useMemoized` to ensure they persist across rebuilds

### 3. **Smart Scroll Detection**
The system determines when to scroll based on:
- **Item Position**: Whether the item is in the bottom 50% of the available screen space
- **Expansion Prediction**: Whether the expanded content might be cut off (estimated at 2x the collapsed height)

### 4. **Smooth Animation**
- Uses `animateTo()` with a 400ms duration and `Curves.easeInOut`
- Positions the expanded item at 25% from the top of the available screen space
- Respects scroll boundaries (won't scroll beyond content limits)

## Implementation Details

### Key Components

```dart
// ScrollController for the ListView
final ScrollController scrollController = useScrollController();

// GlobalKeys for precise positioning
final List<GlobalKey> itemKeys = useMemoized(() {
  return List.generate(
    state.faqModel?.length ?? 0, 
    (index) => GlobalKey(),
  );
}, [state.faqModel?.length]);

// Auto-scroll function
void scrollToItem(int index) {
  // Implementation details...
}
```

### ExpansionTile Integration

```dart
ExpansionTile(
  // ... other properties
  onExpansionChanged: (isExpanded) {
    if (isExpanded) {
      scrollToItem(index);
    }
  },
  // ... children
)
```

### Container Key Assignment

```dart
Container(
  key: index < itemKeys.length ? itemKeys[index] : null,
  // ... other properties
)
```

## Features

### ✅ **Smart Detection**
- Only scrolls when necessary (item is in bottom portion or might be cut off)
- Calculates precise item positions using RenderBox
- Accounts for app bar and system UI elements

### ✅ **Smooth Animation**
- 400ms duration with easeInOut curve
- Positions expanded content optimally (25% from top)
- Respects scroll boundaries

### ✅ **Error Handling**
- Graceful handling of edge cases
- Try-catch blocks prevent crashes
- Null safety checks throughout

### ✅ **Performance Optimized**
- Uses `WidgetsBinding.instance.addPostFrameCallback` for proper timing
- Memoized GlobalKeys prevent unnecessary regeneration
- Only calculates when expansion occurs

## User Experience

### Before Implementation
- Users had to manually scroll to see expanded content
- Content at the bottom of the screen was often cut off
- Poor UX for items near the bottom of the list

### After Implementation
- Automatic scrolling when expanding bottom items
- Always shows full expanded content
- Smooth, intuitive user experience
- No scrolling for items that don't need it

## Configuration Options

The implementation includes several configurable parameters:

```dart
// Threshold for determining "bottom portion" (50% of screen)
final bool isInBottomPortion = itemTop > (availableHeight * 0.5);

// Expansion estimation (2x collapsed height)
final bool mightBeCutOff = (itemTop + itemHeight * 2) > availableHeight;

// Target position after scrolling (25% from top)
final double targetItemPosition = availableHeight * 0.25;

// Animation duration
duration: const Duration(milliseconds: 400)
```

## Alternative Implementation

A reusable `AutoScrollExpansionTile` widget has also been created in:
`lib/features/faq_screen/widgets/auto_scroll_expansion_tile.dart`

This widget can be used in other parts of the app that need similar functionality:

```dart
AutoScrollExpansionTile(
  title: Text('FAQ Question'),
  children: [Text('FAQ Answer')],
  index: index,
  scrollController: scrollController,
  itemKey: itemKeys[index],
  onExpansionChanged: (isExpanded) {
    // Custom callback
  },
)
```

## Testing Scenarios

### ✅ **Tested Cases**
1. **Top Items**: No scrolling occurs (as expected)
2. **Middle Items**: Scrolling only when content might be cut off
3. **Bottom Items**: Always scrolls to show expanded content
4. **Edge Cases**: Handles empty lists, null values, disposed widgets
5. **Performance**: Smooth animation without jank
6. **Boundaries**: Respects scroll limits

### 🧪 **Test Instructions**
1. Open FAQ screen
2. Scroll to bottom of the list
3. Tap on the last few FAQ items
4. Verify that expanded content is fully visible
5. Check that animation is smooth and intuitive

## Benefits

1. **Improved UX**: Users always see full expanded content
2. **Intuitive Behavior**: Automatic scrolling feels natural
3. **Performance**: Only scrolls when necessary
4. **Robust**: Handles edge cases gracefully
5. **Reusable**: Can be applied to other expansion lists
6. **Configurable**: Easy to adjust thresholds and behavior

## Future Enhancements

Potential improvements:
- Dynamic height calculation based on actual expanded content
- Customizable scroll thresholds per item
- Horizontal scrolling support
- Integration with other expandable widgets
- Accessibility improvements for screen readers
