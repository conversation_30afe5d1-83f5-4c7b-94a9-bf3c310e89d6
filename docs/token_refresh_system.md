# Token Refresh System Implementation

## Overview

The Al-Balad parking app now implements an automatic token refresh system that seamlessly handles expired access tokens without requiring users to log in again. This system replaces the previous approach of automatically redirecting users to the sign-in screen on 401 responses.

## Architecture

### Components

1. **TokenRefreshService** - Handles the token refresh logic
2. **AuthInterceptor** - Intercepts 401 responses and triggers token refresh
3. **DioClient** - Configured to work with the token refresh system
4. **Updated API Services** - Use consistent patterns for token handling

## How It Works

### Token Refresh Flow

```mermaid
sequenceDiagram
    participant App as App/Service
    participant Interceptor as AuthInterceptor
    participant RefreshService as TokenRefreshService
    participant API as Backend API
    participant Storage as SecureStorage

    App->>API: API Request with Access Token
    API-->>Interceptor: 401 Unauthorized
    Interceptor->>RefreshService: refreshToken()
    RefreshService->>API: POST /refresh-token/ with refresh_token
    API-->>RefreshService: New tokens
    RefreshService->>Storage: Update stored tokens
    RefreshService->>Interceptor: Return new access token
    Interceptor->>API: Retry original request with new token
    API-->>App: Success response
```

### Fallback to Login

If token refresh fails (e.g., refresh token is expired or invalid):

```mermaid
sequenceDiagram
    participant Interceptor as AuthInterceptor
    participant RefreshService as TokenRefreshService
    participant API as Backend API
    participant Storage as SecureStorage
    participant Navigator as App Navigator

    Interceptor->>RefreshService: refreshToken()
    RefreshService->>API: POST /refresh-token/
    API-->>RefreshService: 401/Error (Invalid refresh token)
    RefreshService-->>Interceptor: null (refresh failed)
    Interceptor->>Storage: Clear all auth data
    Interceptor->>Navigator: Navigate to sign-in screen
```

## API Endpoint

### Refresh Token Endpoint

- **URL**: `{baseURL}refresh-token/`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Body**:
  ```
  refresh_token: {current_refresh_token}
  ```

### Expected Response

**Success (200)**:
```json
{
  "result": "success",
  "token": {
    "access_token": "new_access_token_here",
    "refresh_token": "new_refresh_token_here", 
    "expires_in": "3600"
  }
}
```

**Failure (401/400)**:
```json
{
  "result": "failure",
  "message": "Invalid refresh token"
}
```

## Implementation Details

### TokenRefreshService

Key features:
- **Singleton pattern** - Ensures single instance across the app
- **Concurrency protection** - Prevents multiple simultaneous refresh attempts
- **Automatic token storage** - Updates both memory and secure storage
- **Error handling** - Graceful failure handling

### AuthInterceptor

Enhanced features:
- **Automatic retry** - Retries failed requests with new tokens
- **Clean logout** - Clears all auth data on refresh failure
- **Navigation protection** - Prevents multiple navigation attempts

### Service Integration

Services should use the standard `DioClient` instance:

```dart
class MyService {
  final dio = DioClient().dio; // ✅ Correct - uses token refresh

  Future<Response> getData() async {
    return await dio.get(
      '/api/data',
      options: Options(
        headers: await Api.getAuthorizationHeader(),
        // Don't use validateStatus: (status) => true
      ),
    );
  }
}
```

**Avoid**:
```dart
// ❌ Wrong - bypasses token refresh
options: Options(
  validateStatus: (status) => true,
)

// ❌ Wrong - separate Dio instance without interceptor
final dio = Dio();
```

## Migration Guide

### Services to Update

The following services were identified as needing updates to work properly with the token refresh system:

1. **AuthServices** - Remove `validateStatus: (status) => true` from authenticated endpoints
2. **AllServicesServices** - Remove `validateStatus: (status) => true`
3. **AdServices** - Remove `validateStatus: (status) => true`
4. **SettleAllViolationsServices** - Remove `validateStatus: (status) => true`
5. **PaymentService** - Use `DioClient().dio` instead of separate Dio instance
6. **Support Ticket Provider** - Use `DioClient().dio` instead of separate Dio instance

### Update Pattern

**Before**:
```dart
final response = await dio.post(
  url,
  options: Options(
    validateStatus: (status) => true, // ❌ Remove this
    headers: await Api.getAuthorizationHeader(),
  ),
);
```

**After**:
```dart
final response = await dio.post(
  url,
  options: Options(
    headers: await Api.getAuthorizationHeader(),
    // validateStatus removed - uses default behavior
  ),
);
```

## Testing

### Unit Tests

- **TokenRefreshService** - Tests token refresh logic, error handling, concurrency
- **AuthInterceptor** - Tests 401 handling, retry logic, logout flow
- **Integration Tests** - End-to-end token refresh scenarios

### Test Coverage

- ✅ Successful token refresh
- ✅ Failed token refresh (invalid refresh token)
- ✅ Network errors during refresh
- ✅ Concurrent refresh attempts
- ✅ Token storage and retrieval
- ✅ Automatic logout on refresh failure

## Benefits

1. **Seamless UX** - Users don't get logged out unexpectedly
2. **Automatic handling** - No manual token management needed
3. **Robust error handling** - Graceful fallback to login when needed
4. **Concurrent safety** - Handles multiple simultaneous requests
5. **Secure storage** - Tokens stored securely and updated automatically

## Monitoring and Debugging

### Logging

The system includes comprehensive logging:
- Token refresh attempts
- Success/failure status
- Error details
- Navigation events

### Debug Information

In debug mode, you'll see logs like:
```
[AuthInterceptor] 401 Unauthorized detected, attempting token refresh
[TokenRefreshService] Attempting to refresh token
[TokenRefreshService] Token refreshed successfully
[AuthInterceptor] Token refreshed successfully, retrying original request
```

## Security Considerations

1. **Secure Storage** - Tokens stored in FlutterSecureStorage
2. **Memory Cleanup** - Tokens cleared from memory on logout
3. **Network Security** - HTTPS endpoints for token refresh
4. **Token Rotation** - Both access and refresh tokens are rotated
5. **Automatic Cleanup** - Failed refresh attempts clear all auth data

## Future Enhancements

Potential improvements:
- Token expiration prediction (refresh before expiry)
- Retry logic with exponential backoff
- Analytics for token refresh success rates
- Background token refresh
