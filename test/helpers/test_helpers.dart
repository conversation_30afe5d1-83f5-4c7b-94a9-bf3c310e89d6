/// Test helpers and utilities for the Al-Balad parking app tests
/// Provides common setup and mock data for testing

import 'package:albalad_parking_app/helper/shared_preference_helper.dart';
import 'package:flutter/material.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TestHelpers {
  /// Initialize the test environment with necessary configurations
  static Future<void> initializeTestEnvironment() async {
    // Ensure that the test environment is properly initialized
    TestWidgetsFlutterBinding.ensureInitialized();

    // Mock shared preferences
    SharedPreferences.setMockInitialValues({});

    // Initialize shared preference helper
    await SharedPreferenceHelper.instance.init();
  }

  /// Create a test widget wrapper with providers
  static Widget createTestWidget(Widget child) {
    return MaterialApp(
      home: child,
    );
  }

  /// Mock user data for testing
  static Map<String, dynamic> getMockUserData() {
    return {
      'uid': 'test-user-123',
      'name': 'Test User',
      'email': '<EMAIL>',
      'phone': '+************',
      'token': 'mock-jwt-token',
    };
  }

  /// Mock location data for testing
  static Map<String, dynamic> getMockLocationData() {
    return {
      'location_uid': 'test-location-123',
      'location_name': 'Test Parking Location',
      'latitude': 24.7136,
      'longitude': 46.6753,
      'address': 'Test Address, Riyadh, Saudi Arabia',
      'all_slots': 50,
      'available_slot': 25,
      'location_type': 'On-Street Parking',
      'valet_parking_available': true,
      'one_hr_per_amount': 5.0,
      'price_type': 'hourly',
    };
  }

  /// Mock vehicle data for testing
  static Map<String, dynamic> getMockVehicleData() {
    return {
      'vehicle_id': 'test-vehicle-123',
      'license_plate': 'ABC 1234',
      'vehicle_type': 'Car',
      'vehicle_color': 'White',
      'owner_type': 'Individual',
      'is_primary': true,
    };
  }

  /// Mock parking booking data for testing
  static Map<String, dynamic> getMockParkingBookingData() {
    return {
      'parking_uid': 'test-parking-123',
      'location_uid': 'test-location-123',
      'vehicle_id': 'test-vehicle-123',
      'parking_hours': 2,
      'payment_amount_payed': 10.0,
      'payment_method': 'Wallet',
      'vat_amount': 1.5,
      'parking_start_time': '2024-01-01T10:00:00Z',
      'parking_end_time': '2024-01-01T12:00:00Z',
      'status': 'active',
    };
  }

  /// Mock violation data for testing
  static Map<String, dynamic> getMockViolationData() {
    return {
      'violation_id': 'test-violation-123',
      'vehicle_id': 'test-vehicle-123',
      'violation_type': 'Parking Overtime',
      'fine_amount': 50.0,
      'violation_date': '2024-01-01T15:00:00Z',
      'status': 'pending',
      'location': 'Test Location',
    };
  }

  /// Mock wallet data for testing
  static Map<String, dynamic> getMockWalletData() {
    return {
      'balance': 100.0,
      'currency': 'SAR',
      'last_transaction_date': '2024-01-01T10:00:00Z',
      'transactions': [
        {
          'transaction_id': 'test-txn-123',
          'amount': 50.0,
          'type': 'credit',
          'description': 'Wallet Recharge',
          'date': '2024-01-01T10:00:00Z',
        },
      ],
    };
  }

  /// Clean up test environment
  static Future<void> cleanupTestEnvironment() async {
    // Clear shared preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// Create a mock HTTP response
  static Map<String, dynamic> createMockResponse({
    required String result,
    Map<String, dynamic>? records,
    Map<String, dynamic>? errors,
  }) {
    final response = <String, dynamic>{
      'result': result,
    };

    if (records != null) {
      response['records'] = records;
    }

    if (errors != null) {
      response['errors'] = errors;
    }

    return response;
  }

  /// Create a successful mock response
  static Map<String, dynamic> createSuccessResponse(Map<String, dynamic> data) {
    return createMockResponse(result: 'success', records: data);
  }

  /// Create an error mock response
  static Map<String, dynamic> createErrorResponse(Map<String, dynamic> errors) {
    return createMockResponse(result: 'error', errors: errors);
  }
}

/// Test constants for consistent testing
class TestConstants {
  static const String testApiBaseUrl = 'https://test-api.example.com';
  static const String testUserId = 'test-user-123';
  static const String testUserEmail = '<EMAIL>';
  static const String testUserPhone = '+************';
  static const String testLocationId = 'test-location-123';
  static const String testVehicleId = 'test-vehicle-123';
  static const String testParkingId = 'test-parking-123';
  static const String testViolationId = 'test-violation-123';
  static const double testAmount = 10.0;
  static const int testParkingHours = 2;
}
