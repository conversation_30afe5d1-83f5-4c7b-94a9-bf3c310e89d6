/// Unit tests for error handler
/// Tests error handling and conversion logic

import 'package:albalad_parking_app/core/error/error_handler.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Error Handler Tests', () {
    test('should handle connection timeout error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
        message: 'Connection timeout',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.network));
      expect(appError.isNetworkError, isTrue);
      expect(appError.message, contains('Connection timeout'));
    });

    test('should handle send timeout error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.sendTimeout,
        message: 'Send timeout',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.network));
      expect(appError.message, contains('timeout'));
    });

    test('should handle receive timeout error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.receiveTimeout,
        message: 'Receive timeout',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.network));
      expect(appError.message, contains('timeout'));
    });

    test('should handle connection error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionError,
        message: 'Connection error',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.network));
      expect(appError.isNetworkError, isTrue);
      expect(appError.message, contains('internet'));
    });

    test('should handle 400 bad request error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 400,
        data: {'message': 'Invalid request data'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.validation));
      expect(appError.isValidationError, isTrue);
      expect(appError.message, equals('Invalid request data'));
    });

    test('should handle 401 unauthorized error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 401,
        data: {'message': 'Unauthorized'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.authentication));
      expect(appError.isAuthError, isTrue);
      expect(appError.message, contains('Session expired'));
    });

    test('should handle 403 forbidden error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 403,
        data: {'message': 'Forbidden'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.authorization));
      expect(appError.message, contains('Access denied'));
    });

    test('should handle 404 not found error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 404,
        data: {'message': 'Not found'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.notFound));
      expect(appError.message, contains('not found'));
    });

    test('should handle 422 validation error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 422,
        data: {
          'errors': {
            'email': 'Email is required',
            'password': 'Password is too short',
          }
        },
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.validation));
      expect(appError.isValidationError, isTrue);
      expect(appError.message, equals('Email is required'));
    });

    test('should handle 500 server error', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 500,
        data: {'message': 'Internal server error'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.server));
      expect(appError.isServerError, isTrue);
      expect(appError.message, contains('Server error'));
    });

    test('should handle cancelled request', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.cancel,
        message: 'Request cancelled',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.cancelled));
      expect(appError.message, contains('cancelled'));
    });

    test('should handle bad certificate error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badCertificate,
        message: 'Bad certificate',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.security));
      expect(appError.message, contains('certificate'));
    });

    test('should handle unknown error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.unknown,
        message: 'Unknown error',
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.type, equals(ErrorType.unknown));
      expect(appError.message, contains('unexpected error'));
    });

    test('should handle generic exception', () {
      final exception = Exception('Generic error');

      final appError = ErrorHandler.handleGenericException(exception);

      expect(appError.type, equals(ErrorType.unknown));
      expect(appError.message, contains('unexpected error'));
      expect(appError.originalError, equals(exception));
    });

    test('should extract error message from response data', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 400,
        data: {
          'errors': ['First error', 'Second error']
        },
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.message, equals('First error'));
    });

    test('should handle response with error field', () {
      final response = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 400,
        data: {'error': 'Custom error message'},
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: response,
      );

      final appError = ErrorHandler.handleDioException(dioError);

      expect(appError.message, equals('Custom error message'));
    });
  });

  group('AppError Tests', () {
    test('should create AppError correctly', () {
      final error = AppError(
        type: ErrorType.network,
        message: 'Network error',
        originalError: Exception('Original'),
      );

      expect(error.type, equals(ErrorType.network));
      expect(error.message, equals('Network error'));
      expect(error.isNetworkError, isTrue);
      expect(error.isAuthError, isFalse);
      expect(error.isValidationError, isFalse);
      expect(error.isServerError, isFalse);
    });

    test('should identify error types correctly', () {
      final networkError =
          AppError(type: ErrorType.network, message: 'Network');
      final authError =
          AppError(type: ErrorType.authentication, message: 'Auth');
      final validationError =
          AppError(type: ErrorType.validation, message: 'Validation');
      final serverError = AppError(type: ErrorType.server, message: 'Server');

      expect(networkError.isNetworkError, isTrue);
      expect(authError.isAuthError, isTrue);
      expect(validationError.isValidationError, isTrue);
      expect(serverError.isServerError, isTrue);
    });
  });
}
