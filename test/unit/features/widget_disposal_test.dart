/// Test for widget disposal and ref usage safety
/// Ensures that ref is not used after widget disposal

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() {
  group('Widget Disposal Safety Tests', () {
    testWidgets('should handle mounted check in async operations',
        (WidgetTester tester) async {
      bool asyncOperationCompleted = false;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: TestWidgetWithAsyncOp(
              onAsyncComplete: () {
                asyncOperationCompleted = true;
              },
            ),
          ),
        ),
      );

      // Verify widget is mounted
      expect(find.byType(TestWidgetWithAsyncOp), findsOneWidget);

      // Trigger async operation
      await tester.tap(find.byKey(const Key('trigger_async')));
      await tester.pump();

      // Wait a bit for async operation to start
      await tester.pump(const Duration(milliseconds: 100));

      // Dispose the widget before async operation completes
      await tester.pumpWidget(const MaterialApp(home: SizedBox()));

      // Wait for async operation duration to pass
      await tester.pump(const Duration(seconds: 2));

      // Verify async operation did NOT complete due to mounted check
      expect(asyncOperationCompleted, isFalse);
    });

    testWidgets('should handle timer disposal correctly',
        (WidgetTester tester) async {
      bool timerCallbackExecuted = false;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: TestWidgetWithTimer(
              onTimerCallback: () {
                timerCallbackExecuted = true;
              },
            ),
          ),
        ),
      );

      // Start timer
      await tester.tap(find.byKey(const Key('start_timer')));
      await tester.pump();

      // Dispose widget before timer completes
      await tester.pumpWidget(const MaterialApp(home: SizedBox()));

      // Wait for timer duration
      await tester.pump(const Duration(milliseconds: 1100));

      // Timer callback should not have executed after disposal
      expect(timerCallbackExecuted, isFalse);
    });

    test('should validate mounted check pattern', () {
      // Test the mounted check pattern used in our fixes
      bool mounted = true;
      bool operationExecuted = false;

      // Simulate async operation with mounted check
      void simulateAsyncOperation() {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            operationExecuted = true;
          }
        });
      }

      simulateAsyncOperation();

      // Simulate widget disposal
      mounted = false;

      // Wait for async operation
      Future.delayed(const Duration(milliseconds: 200), () {
        expect(operationExecuted, isFalse);
      });
    });
  });
}

/// Test widget that simulates async operations with ref usage
class TestWidgetWithAsyncOp extends ConsumerStatefulWidget {
  final VoidCallback onAsyncComplete;

  const TestWidgetWithAsyncOp({
    super.key,
    required this.onAsyncComplete,
  });

  @override
  ConsumerState<TestWidgetWithAsyncOp> createState() =>
      _TestWidgetWithAsyncOpState();
}

class _TestWidgetWithAsyncOpState extends ConsumerState<TestWidgetWithAsyncOp> {
  void _triggerAsyncOperation() {
    Future.delayed(const Duration(seconds: 1), () {
      // Simulate the mounted check pattern
      if (mounted) {
        // This would normally use ref, but we'll just call the callback
        widget.onAsyncComplete();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: ElevatedButton(
          key: const Key('trigger_async'),
          onPressed: _triggerAsyncOperation,
          child: const Text('Trigger Async'),
        ),
      ),
    );
  }
}

/// Test widget that simulates timer usage with ref
class TestWidgetWithTimer extends ConsumerStatefulWidget {
  final VoidCallback onTimerCallback;

  const TestWidgetWithTimer({
    super.key,
    required this.onTimerCallback,
  });

  @override
  ConsumerState<TestWidgetWithTimer> createState() =>
      _TestWidgetWithTimerState();
}

class _TestWidgetWithTimerState extends ConsumerState<TestWidgetWithTimer> {
  Timer? _timer;

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        // This would normally use ref, but we'll just call the callback
        widget.onTimerCallback();
      } else {
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: ElevatedButton(
          key: const Key('start_timer'),
          onPressed: _startTimer,
          child: const Text('Start Timer'),
        ),
      ),
    );
  }
}

/// Simple provider for testing
final testProvider = StateProvider<int>((ref) => 0);
