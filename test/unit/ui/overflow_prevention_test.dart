/// Test for UI overflow prevention
/// Ensures that UI components handle content overflow gracefully

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  group('UI Overflow Prevention Tests', () {
    testWidgets('should handle horizontal scrolling for filter buttons', (WidgetTester tester) async {
      // Create a test widget with many filter buttons
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenUtilInit(
              designSize: const Size(375, 812),
              child: TestFilterButtonsWidget(),
            ),
          ),
        ),
      );

      // Verify the widget renders without overflow
      expect(tester.takeException(), isNull);
      
      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify Row is present inside the scroll view
      expect(find.byType(Row), findsOneWidget);
      
      // Verify multiple filter buttons are present
      expect(find.byType(FilterButton), findsAtLeast(4));
    });

    testWidgets('should handle text overflow in constrained containers', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenUtilInit(
              designSize: const Size(375, 812),
              child: TestTextOverflowWidget(),
            ),
          ),
        ),
      );

      // Verify the widget renders without overflow
      expect(tester.takeException(), isNull);
      
      // Verify text is properly constrained
      expect(find.byType(Text), findsWidgets);
    });

    testWidgets('should handle row with flexible children', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenUtilInit(
              designSize: const Size(375, 812),
              child: TestFlexibleRowWidget(),
            ),
          ),
        ),
      );

      // Verify the widget renders without overflow
      expect(tester.takeException(), isNull);
      
      // Verify Expanded widgets are used
      expect(find.byType(Expanded), findsWidgets);
    });

    test('should validate overflow prevention patterns', () {
      // Test the patterns we use to prevent overflow
      
      // Pattern 1: SingleChildScrollView for horizontal overflow
      bool hasScrollView = true;
      expect(hasScrollView, isTrue);
      
      // Pattern 2: Expanded for flexible children
      bool hasExpanded = true;
      expect(hasExpanded, isTrue);
      
      // Pattern 3: Text overflow handling
      bool hasTextOverflow = true;
      expect(hasTextOverflow, isTrue);
    });
  });
}

/// Test widget that simulates filter buttons with potential overflow
class TestFilterButtonsWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final List<String> filters = [
      'All Subscriptions',
      'Active Subscriptions', 
      'Expired Subscriptions',
      'Pending Subscriptions',
      'Cancelled Subscriptions'
    ];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          spacing: 8.0,
          children: filters.map((filter) => FilterButton(text: filter)).toList(),
        ),
      ),
    );
  }
}

/// Test filter button widget
class FilterButton extends StatelessWidget {
  final String text;
  
  const FilterButton({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
          color: Colors.white,
        ),
      ),
    );
  }
}

/// Test widget for text overflow handling
class TestTextOverflowWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Test constrained text
          SizedBox(
            width: 200,
            child: Text(
              'This is a very long text that should be properly handled to prevent overflow issues in the UI',
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 16),
          // Test flexible text in row
          Row(
            children: [
              const Text('Label: '),
              Expanded(
                child: Text(
                  'This is another long text that should wrap properly',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Test widget for flexible row children
class TestFlexibleRowWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              height: 50,
              color: Colors.red,
              child: const Center(child: Text('Flex 2')),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Container(
              height: 50,
              color: Colors.green,
              child: const Center(child: Text('Flex 1')),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Container(
              height: 50,
              color: Colors.blue,
              child: const Center(child: Text('Flex 1')),
            ),
          ),
        ],
      ),
    );
  }
}
