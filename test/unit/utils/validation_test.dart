/// Unit tests for validation utilities
/// Tests all validation functions to ensure they work correctly

import 'package:albalad_parking_app/utils/validations.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Email Validation Tests', () {
    test('should validate correct email addresses', () {
      expect('<EMAIL>'.isValidEmail(), isTrue);
      expect('<EMAIL>'.isValidEmail(), isTrue);
      expect('<EMAIL>'.isValidEmail(), isTrue);
    });

    test('should reject invalid email addresses', () {
      expect(''.isValidEmail(), isFalse);
      expect('invalid-email'.isValidEmail(), isFalse);
      expect('@example.com'.isValidEmail(), isFalse);
      expect('test@'.isValidEmail(), isFalse);
      expect('<EMAIL>'.isValidEmail(), isFalse);
    });
  });

  group('Phone Number Validation Tests', () {
    test('should validate correct phone numbers', () {
      expect('1234567'.isValidPhoneNumber(), isTrue);
      expect('123456789012345'.isValidPhoneNumber(), isTrue);
      expect('+966501234567'.isValidPhoneNumber(), isTrue);
    });

    test('should reject invalid phone numbers', () {
      expect(''.isValidPhoneNumber(), isFalse);
      expect('123456'.isValidPhoneNumber(), isFalse); // Too short
      expect('1234567890123456'.isValidPhoneNumber(), isFalse); // Too long
    });

    test('should validate Saudi phone numbers', () {
      expect('501234567'.isValidSaudiPhoneNumber(), isTrue);
      expect('966501234567'.isValidSaudiPhoneNumber(), isTrue);
      expect('+966501234567'.isValidSaudiPhoneNumber(), isTrue);
    });

    test('should reject invalid Saudi phone numbers', () {
      expect(''.isValidSaudiPhoneNumber(), isFalse);
      expect('401234567'.isValidSaudiPhoneNumber(),
          isFalse); // Doesn't start with 5
      expect('50123456'.isValidSaudiPhoneNumber(), isFalse); // Too short
      expect('5012345678'.isValidSaudiPhoneNumber(), isFalse); // Too long
    });
  });

  group('Name Validation Tests', () {
    test('should validate correct names', () {
      expect('John Doe'.isValidName(), isTrue);
      expect('محمد أحمد'.isValidName(), isTrue);
      expect("O'Connor".isValidName(), isTrue);
      expect('Jean-Pierre'.isValidName(), isTrue);
    });

    test('should reject invalid names', () {
      expect(''.isValidName(), isFalse);
      expect('A'.isValidName(), isFalse); // Too short
      expect('John123'.isValidName(), isFalse); // Contains numbers
      expect(('A' * 51).isValidName(), isFalse); // Too long
    });

    test('should validate Arabic names', () {
      expect('محمد أحمد'.isValidArabicName(), isTrue);
      expect('فاطمة الزهراء'.isValidArabicName(), isTrue);
    });

    test('should validate English names', () {
      expect('John Doe'.isValidEnglishName(), isTrue);
      expect("O'Connor".isValidEnglishName(), isTrue);
      expect('Jean-Pierre'.isValidEnglishName(), isTrue);
    });
  });

  group('Password Validation Tests', () {
    test('should validate correct passwords', () {
      expect('password123'.isValidPassword(), isTrue);
      expect('MyPassword1'.isValidPassword(), isTrue);
    });

    test('should reject invalid passwords', () {
      expect(''.isValidPassword(), isFalse);
      expect('short'.isValidPassword(), isFalse); // Too short
      expect('password'.isValidPassword(), isFalse); // No numbers
      expect('12345678'.isValidPassword(), isFalse); // No letters
    });

    test('should validate strong passwords', () {
      expect('MyPassword123!'.isStrongPassword(), isTrue);
      expect('StrongP@ss1'.isStrongPassword(), isTrue);
    });

    test('should reject weak passwords', () {
      expect('password123'.isStrongPassword(),
          isFalse); // No uppercase or special char
      expect('PASSWORD123!'.isStrongPassword(), isFalse); // No lowercase
      expect('MyPassword!'.isStrongPassword(), isFalse); // No numbers
    });
  });

  group('Card Number Validation Tests', () {
    test('should validate correct card numbers', () {
      expect('****************'.isValidCardNumber(), isTrue); // Visa test card
      expect('****************'.isValidCardNumber(),
          isTrue); // Mastercard test card
    });

    test('should reject invalid card numbers', () {
      expect(''.isValidCardNumber(), isFalse);
      expect('1234567890123456'.isValidCardNumber(), isFalse); // Invalid Luhn
      expect('123456789012'.isValidCardNumber(), isFalse); // Too short
      expect('12345678901234567890'.isValidCardNumber(), isFalse); // Too long
    });

    test('should identify card types', () {
      expect('****************'.getCardType(), equals('Visa'));
      expect('****************'.getCardType(), equals('Mastercard'));
      expect('***************'.getCardType(), equals('American Express'));
      expect('****************'.getCardType(), equals('Discover'));
      expect('1234567890123456'.getCardType(), equals('Unknown'));
    });
  });

  group('License Plate Validation Tests', () {
    test('should validate Saudi license plates', () {
      expect('أبج 123'.isValidSaudiLicensePlate(), isTrue);
      expect('أبج1234'.isValidSaudiLicensePlate(), isTrue);
    });

    test('should validate international license plates', () {
      expect('ABC123'.isValidInternationalLicensePlate(), isTrue);
      expect('AB-123'.isValidInternationalLicensePlate(), isTrue);
      expect('أبج123'.isValidInternationalLicensePlate(), isTrue);
    });
  });

  group('Amount Validation Tests', () {
    test('should validate correct amounts', () {
      expect('10.50'.isValidAmount(), isTrue);
      expect('100'.isValidAmount(), isTrue);
      expect('0.01'.isValidAmount(), isTrue);
    });

    test('should reject invalid amounts', () {
      expect(''.isValidAmount(), isFalse);
      expect('0'.isValidAmount(), isFalse); // Zero amount
      expect('-10'.isValidAmount(), isFalse); // Negative amount
      expect('abc'.isValidAmount(), isFalse); // Not a number
    });

    test('should validate amounts within range', () {
      expect('50'.isValidAmount(minAmount: 10, maxAmount: 100), isTrue);
      expect('5'.isValidAmount(minAmount: 10, maxAmount: 100), isFalse);
      expect('150'.isValidAmount(minAmount: 10, maxAmount: 100), isFalse);
    });
  });

  group('Validation Utils Tests', () {
    test('should check if string is not empty', () {
      expect(ValidationUtils.isNotEmpty('test'), isTrue);
      expect(ValidationUtils.isNotEmpty(''), isFalse);
      expect(ValidationUtils.isNotEmpty(null), isFalse);
      expect(ValidationUtils.isNotEmpty('   '), isFalse); // Only whitespace
    });

    test('should check minimum length', () {
      expect(ValidationUtils.hasMinLength('test', 3), isTrue);
      expect(ValidationUtils.hasMinLength('test', 5), isFalse);
      expect(ValidationUtils.hasMinLength(null, 3), isFalse);
    });

    test('should check maximum length', () {
      expect(ValidationUtils.hasMaxLength('test', 5), isTrue);
      expect(ValidationUtils.hasMaxLength('test', 3), isFalse);
      expect(ValidationUtils.hasMaxLength(null, 5), isFalse);
    });

    test('should check if string is numeric', () {
      expect(ValidationUtils.isNumeric('123'), isTrue);
      expect(ValidationUtils.isNumeric('123abc'), isFalse);
      expect(ValidationUtils.isNumeric(''), isFalse);
    });

    test('should check if string is alphabetic', () {
      expect(ValidationUtils.isAlphabetic('abc'), isTrue);
      expect(ValidationUtils.isAlphabetic('محمد'), isTrue);
      expect(ValidationUtils.isAlphabetic('abc123'), isFalse);
      expect(ValidationUtils.isAlphabetic(''), isFalse);
    });

    test('should validate OTP', () {
      expect(ValidationUtils.isValidOTP('123456'), isTrue);
      expect(ValidationUtils.isValidOTP('1234', length: 4), isTrue);
      expect(ValidationUtils.isValidOTP('12345'), isFalse); // Wrong length
      expect(ValidationUtils.isValidOTP('12345a'), isFalse); // Contains letters
    });

    test('should validate URLs', () {
      expect(ValidationUtils.isValidUrl('https://example.com'), isTrue);
      expect(ValidationUtils.isValidUrl('http://www.example.com'), isTrue);
      expect(ValidationUtils.isValidUrl('invalid-url'), isFalse);
      expect(ValidationUtils.isValidUrl(''), isFalse);
    });
  });
}
